{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\email_dash\\\\client\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { contactAPI, listAPI } from '../services/api';\nimport ContactForm from './ContactForm';\nimport CSVImport from './CSVImport';\nimport ListForm from './ListForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [lists, setLists] = useState([]);\n  const [contacts, setContacts] = useState([]);\n  const [selectedListId, setSelectedListId] = useState('default');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showContactForm, setShowContactForm] = useState(false);\n  const [showCSVImport, setShowCSVImport] = useState(false);\n  const [showListForm, setShowListForm] = useState(false);\n  const [editingList, setEditingList] = useState(null);\n  const navigate = useNavigate();\n  useEffect(() => {\n    loadData();\n  }, []);\n  useEffect(() => {\n    if (selectedListId) {\n      loadContacts();\n    }\n  }, [selectedListId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [listsResponse, contactsResponse] = await Promise.all([listAPI.getAll(), contactAPI.getAll()]);\n      setLists(listsResponse.data);\n      setContacts(contactsResponse.data);\n      setError(null);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadContacts = async () => {\n    try {\n      const response = await contactAPI.getAll();\n      const filteredContacts = response.data.filter(c => c.listId === selectedListId);\n      setContacts(filteredContacts);\n    } catch (err) {\n      setError(err.message);\n    }\n  };\n  const handleContactClick = contactId => {\n    navigate(`/contact/${contactId}`);\n  };\n  const handleContactCreated = () => {\n    setShowContactForm(false);\n    loadData();\n  };\n  const handleCSVImported = () => {\n    setShowCSVImport(false);\n    loadData();\n  };\n  const handleListCreated = () => {\n    setShowListForm(false);\n    setEditingList(null);\n    loadData();\n  };\n  const handleEditList = list => {\n    setEditingList(list);\n    setShowListForm(true);\n  };\n  const handleDeleteList = async listId => {\n    if (listId === 'default') {\n      alert('Cannot delete the default contact list');\n      return;\n    }\n    const list = lists.find(l => l.id === listId);\n    const contactCount = (list === null || list === void 0 ? void 0 : list.contactCount) || 0;\n    let confirmMessage = `Are you sure you want to delete the list \"${list === null || list === void 0 ? void 0 : list.name}\"?`;\n    if (contactCount > 0) {\n      confirmMessage += `\\n\\nThis list contains ${contactCount} contact(s). They will be moved to the \"General Contacts\" list.`;\n    }\n    if (window.confirm(confirmMessage)) {\n      try {\n        const response = await listAPI.delete(listId);\n\n        // Show success message with moved contacts info\n        if (response.data.movedContacts > 0) {\n          alert(`List deleted successfully. ${response.data.movedContacts} contact(s) were moved to \"General Contacts\".`);\n        }\n\n        // Switch to default list if we deleted the currently selected list\n        if (selectedListId === listId) {\n          setSelectedListId('default');\n        }\n        loadData();\n      } catch (err) {\n        setError(err.message);\n        alert('Failed to delete list: ' + err.message);\n      }\n    }\n  };\n  const handleDeleteContact = async contactId => {\n    if (window.confirm('Are you sure you want to delete this contact?')) {\n      try {\n        await contactAPI.delete(contactId);\n        loadData();\n      } catch (err) {\n        setError(err.message);\n      }\n    }\n  };\n  const getCurrentList = () => {\n    return lists.find(l => l.id === selectedListId) || {\n      name: 'Unknown List',\n      color: '#6c757d'\n    };\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'Never';\n    return new Date(dateString).toLocaleDateString();\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '2rem'\n        },\n        children: \"Loading contacts...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '2rem',\n          color: 'red'\n        },\n        children: [\"Error: \", error, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: loadContacts,\n          style: {\n            marginTop: '1rem'\n          },\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this);\n  }\n  const currentList = getCurrentList();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '2rem',\n        borderBottom: '1px solid #dee2e6',\n        paddingBottom: '1rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"list-navigation\",\n        style: {\n          display: 'flex',\n          flexWrap: 'wrap',\n          gap: '0.5rem',\n          alignItems: 'center',\n          overflowX: 'auto',\n          paddingBottom: '0.5rem'\n        },\n        children: [lists.map(list => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'relative',\n            display: 'inline-block'\n          },\n          className: \"list-tab\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedListId(list.id),\n            \"data-count\": `(${list.contactCount || 0})`,\n            style: {\n              padding: '0.5rem 1rem',\n              paddingRight: list.id !== 'default' ? '3rem' : '1rem',\n              border: 'none',\n              borderRadius: '6px',\n              backgroundColor: selectedListId === list.id ? list.color : '#f8f9fa',\n              color: selectedListId === list.id ? 'white' : '#495057',\n              cursor: 'pointer',\n              fontWeight: selectedListId === list.id ? 'bold' : 'normal',\n              transition: 'all 0.2s',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem',\n              fontSize: '0.9rem',\n              whiteSpace: 'nowrap',\n              minWidth: 'fit-content',\n              maxWidth: '200px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                width: '10px',\n                height: '10px',\n                borderRadius: '50%',\n                backgroundColor: selectedListId === list.id ? 'rgba(255,255,255,0.3)' : list.color,\n                flexShrink: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                overflow: 'hidden',\n                textOverflow: 'ellipsis'\n              },\n              children: [list.name, \" (\", list.contactCount || 0, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), list.id !== 'default' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"list-actions\",\n            style: {\n              position: 'absolute',\n              top: '4px',\n              right: '4px',\n              display: 'flex',\n              gap: '2px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleEditList(list);\n              },\n              style: {\n                width: '18px',\n                height: '18px',\n                border: 'none',\n                borderRadius: '50%',\n                backgroundColor: 'rgba(52, 152, 219, 0.8)',\n                color: 'white',\n                cursor: 'pointer',\n                fontSize: '9px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transition: 'all 0.2s'\n              },\n              title: `Edit ${list.name} list`,\n              onMouseEnter: e => {\n                e.target.style.backgroundColor = '#3498db';\n              },\n              onMouseLeave: e => {\n                e.target.style.backgroundColor = 'rgba(52, 152, 219, 0.8)';\n              },\n              children: \"\\u270E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: e => {\n                e.stopPropagation();\n                handleDeleteList(list.id);\n              },\n              style: {\n                width: '18px',\n                height: '18px',\n                border: 'none',\n                borderRadius: '50%',\n                backgroundColor: 'rgba(220, 53, 69, 0.8)',\n                color: 'white',\n                cursor: 'pointer',\n                fontSize: '11px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transition: 'all 0.2s'\n              },\n              title: `Delete ${list.name} list`,\n              onMouseEnter: e => {\n                e.target.style.backgroundColor = '#dc3545';\n              },\n              onMouseLeave: e => {\n                e.target.style.backgroundColor = 'rgba(220, 53, 69, 0.8)';\n              },\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this)]\n        }, list.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowListForm(true),\n          style: {\n            padding: '0.5rem 1rem',\n            border: '2px dashed #dee2e6',\n            borderRadius: '6px',\n            backgroundColor: 'transparent',\n            color: '#6c757d',\n            cursor: 'pointer',\n            transition: 'all 0.2s',\n            fontSize: '0.9rem',\n            whiteSpace: 'nowrap'\n          },\n          children: \"+ Add List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: currentList.color\n        },\n        children: [currentList.name, \" (\", contacts.length, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-success\",\n          onClick: () => setShowContactForm(true),\n          style: {\n            marginRight: '1rem'\n          },\n          children: \"Add Contact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowCSVImport(true),\n          children: \"Import CSV\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), contacts.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '3rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"No contacts yet\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Add your first contact or import from CSV to get started.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"contact-list\",\n      children: contacts.map(contact => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contact-card\",\n        style: {\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          onClick: () => handleContactClick(contact.id),\n          style: {\n            cursor: 'pointer'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-name\",\n            children: contact.firstName && contact.lastName ? `${contact.firstName} ${contact.lastName}` : contact.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-email\",\n            children: contact.email\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 17\n          }, this), (contact.company || contact.title) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6c757d',\n              marginBottom: '0.5rem',\n              fontSize: '0.9rem'\n            },\n            children: contact.title && contact.company ? `${contact.title} at ${contact.company}` : contact.title || contact.company\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 19\n          }, this), contact.phone && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#6c757d',\n              marginBottom: '0.5rem'\n            },\n            children: [\"\\uD83D\\uDCDE \", contact.phone]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 19\n          }, this), (contact.linkedinUrl || contact.website) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '0.5rem',\n              marginBottom: '0.5rem',\n              fontSize: '0.8rem'\n            },\n            children: [contact.linkedinUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: contact.linkedinUrl,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                color: '#0077b5',\n                textDecoration: 'none'\n              },\n              onClick: e => e.stopPropagation(),\n              children: \"LinkedIn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 23\n            }, this), contact.website && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: contact.website,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                color: '#3498db',\n                textDecoration: 'none'\n              },\n              onClick: e => e.stopPropagation(),\n              children: \"Website\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 23\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `contact-status ${contact.status}`,\n            children: contact.status\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"contact-meta\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Conversations: \", contact.conversationCount || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Last contact: \", formatDate(contact.lastContactDate)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Added: \", formatDate(contact.createdAt)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: e => {\n            e.stopPropagation();\n            handleDeleteContact(contact.id);\n          },\n          style: {\n            position: 'absolute',\n            top: '0.5rem',\n            right: '0.5rem',\n            background: '#dc3545',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            width: '24px',\n            height: '24px',\n            fontSize: '12px',\n            cursor: 'pointer',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          title: \"Delete contact\",\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 15\n        }, this)]\n      }, contact.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 9\n    }, this), showContactForm && /*#__PURE__*/_jsxDEV(ContactForm, {\n      onClose: () => setShowContactForm(false),\n      onContactCreated: handleContactCreated,\n      defaultListId: selectedListId,\n      lists: lists\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 460,\n      columnNumber: 9\n    }, this), showCSVImport && /*#__PURE__*/_jsxDEV(CSVImport, {\n      onClose: () => setShowCSVImport(false),\n      onImportComplete: handleCSVImported,\n      defaultListId: selectedListId,\n      lists: lists\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 9\n    }, this), showListForm && /*#__PURE__*/_jsxDEV(ListForm, {\n      onClose: () => {\n        setShowListForm(false);\n        setEditingList(null);\n      },\n      onListCreated: handleListCreated,\n      list: editingList\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"vja7f0WAdIj/g2Vl26Mpew24+Zc=\", false, function () {\n  return [useNavigate];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "contactAPI", "listAPI", "ContactForm", "CSVImport", "ListForm", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "lists", "setLists", "contacts", "setContacts", "selectedListId", "setSelectedListId", "loading", "setLoading", "error", "setError", "showContactForm", "setShowContactForm", "showCSVImport", "setShowCSVImport", "showListForm", "setShowListForm", "editingList", "setEditingList", "navigate", "loadData", "loadContacts", "listsResponse", "contactsResponse", "Promise", "all", "getAll", "data", "err", "message", "response", "filteredContacts", "filter", "c", "listId", "handleContactClick", "contactId", "handleContactCreated", "handleCSVImported", "handleListCreated", "handleEditList", "list", "handleDeleteList", "alert", "find", "l", "id", "contactCount", "confirmMessage", "name", "window", "confirm", "delete", "movedContacts", "handleDeleteContact", "getCurrentList", "color", "formatDate", "dateString", "Date", "toLocaleDateString", "className", "children", "style", "textAlign", "padding", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "marginTop", "currentList", "marginBottom", "borderBottom", "paddingBottom", "display", "flexWrap", "gap", "alignItems", "overflowX", "map", "position", "paddingRight", "border", "borderRadius", "backgroundColor", "cursor", "fontWeight", "transition", "fontSize", "whiteSpace", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "width", "height", "flexShrink", "overflow", "textOverflow", "top", "right", "e", "stopPropagation", "justifyContent", "title", "onMouseEnter", "target", "onMouseLeave", "length", "marginRight", "contact", "firstName", "lastName", "email", "company", "phone", "linkedinUrl", "website", "href", "rel", "textDecoration", "status", "conversationCount", "lastContactDate", "createdAt", "background", "onClose", "onContactCreated", "defaultListId", "onImportComplete", "onListCreated", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/email_dash/client/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { contactAPI, listAPI } from '../services/api';\nimport ContactForm from './ContactForm';\nimport CSVImport from './CSVImport';\nimport ListForm from './ListForm';\n\nconst Dashboard = () => {\n  const [lists, setLists] = useState([]);\n  const [contacts, setContacts] = useState([]);\n  const [selectedListId, setSelectedListId] = useState('default');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showContactForm, setShowContactForm] = useState(false);\n  const [showCSVImport, setShowCSVImport] = useState(false);\n  const [showListForm, setShowListForm] = useState(false);\n  const [editingList, setEditingList] = useState(null);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  useEffect(() => {\n    if (selectedListId) {\n      loadContacts();\n    }\n  }, [selectedListId]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadData = async () => {\n    try {\n      setLoading(true);\n      const [listsResponse, contactsResponse] = await Promise.all([\n        listAPI.getAll(),\n        contactAPI.getAll()\n      ]);\n      setLists(listsResponse.data);\n      setContacts(contactsResponse.data);\n      setError(null);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadContacts = async () => {\n    try {\n      const response = await contactAPI.getAll();\n      const filteredContacts = response.data.filter(c => c.listId === selectedListId);\n      setContacts(filteredContacts);\n    } catch (err) {\n      setError(err.message);\n    }\n  };\n\n  const handleContactClick = (contactId) => {\n    navigate(`/contact/${contactId}`);\n  };\n\n  const handleContactCreated = () => {\n    setShowContactForm(false);\n    loadData();\n  };\n\n  const handleCSVImported = () => {\n    setShowCSVImport(false);\n    loadData();\n  };\n\n  const handleListCreated = () => {\n    setShowListForm(false);\n    setEditingList(null);\n    loadData();\n  };\n\n  const handleEditList = (list) => {\n    setEditingList(list);\n    setShowListForm(true);\n  };\n\n  const handleDeleteList = async (listId) => {\n    if (listId === 'default') {\n      alert('Cannot delete the default contact list');\n      return;\n    }\n\n    const list = lists.find(l => l.id === listId);\n    const contactCount = list?.contactCount || 0;\n\n    let confirmMessage = `Are you sure you want to delete the list \"${list?.name}\"?`;\n    if (contactCount > 0) {\n      confirmMessage += `\\n\\nThis list contains ${contactCount} contact(s). They will be moved to the \"General Contacts\" list.`;\n    }\n\n    if (window.confirm(confirmMessage)) {\n      try {\n        const response = await listAPI.delete(listId);\n\n        // Show success message with moved contacts info\n        if (response.data.movedContacts > 0) {\n          alert(`List deleted successfully. ${response.data.movedContacts} contact(s) were moved to \"General Contacts\".`);\n        }\n\n        // Switch to default list if we deleted the currently selected list\n        if (selectedListId === listId) {\n          setSelectedListId('default');\n        }\n\n        loadData();\n      } catch (err) {\n        setError(err.message);\n        alert('Failed to delete list: ' + err.message);\n      }\n    }\n  };\n\n  const handleDeleteContact = async (contactId) => {\n    if (window.confirm('Are you sure you want to delete this contact?')) {\n      try {\n        await contactAPI.delete(contactId);\n        loadData();\n      } catch (err) {\n        setError(err.message);\n      }\n    }\n  };\n\n  const getCurrentList = () => {\n    return lists.find(l => l.id === selectedListId) || { name: 'Unknown List', color: '#6c757d' };\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'Never';\n    return new Date(dateString).toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"dashboard\">\n        <div style={{ textAlign: 'center', padding: '2rem' }}>\n          Loading contacts...\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"dashboard\">\n        <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>\n          Error: {error}\n          <br />\n          <button className=\"btn btn-primary\" onClick={loadContacts} style={{ marginTop: '1rem' }}>\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentList = getCurrentList();\n\n  return (\n    <div className=\"dashboard\">\n      {/* List Navigation */}\n      <div style={{\n        marginBottom: '2rem',\n        borderBottom: '1px solid #dee2e6',\n        paddingBottom: '1rem'\n      }}>\n        <div\n          className=\"list-navigation\"\n          style={{\n            display: 'flex',\n            flexWrap: 'wrap',\n            gap: '0.5rem',\n            alignItems: 'center',\n            overflowX: 'auto',\n            paddingBottom: '0.5rem'\n          }}>\n        {lists.map((list) => (\n          <div\n            key={list.id}\n            style={{\n              position: 'relative',\n              display: 'inline-block'\n            }}\n            className=\"list-tab\"\n          >\n            <button\n              onClick={() => setSelectedListId(list.id)}\n              data-count={`(${list.contactCount || 0})`}\n              style={{\n                padding: '0.5rem 1rem',\n                paddingRight: list.id !== 'default' ? '3rem' : '1rem',\n                border: 'none',\n                borderRadius: '6px',\n                backgroundColor: selectedListId === list.id ? list.color : '#f8f9fa',\n                color: selectedListId === list.id ? 'white' : '#495057',\n                cursor: 'pointer',\n                fontWeight: selectedListId === list.id ? 'bold' : 'normal',\n                transition: 'all 0.2s',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.9rem',\n                whiteSpace: 'nowrap',\n                minWidth: 'fit-content',\n                maxWidth: '200px'\n              }}\n            >\n              <span\n                style={{\n                  width: '10px',\n                  height: '10px',\n                  borderRadius: '50%',\n                  backgroundColor: selectedListId === list.id ? 'rgba(255,255,255,0.3)' : list.color,\n                  flexShrink: 0\n                }}\n              />\n              <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>\n                {list.name} ({list.contactCount || 0})\n              </span>\n            </button>\n\n            {/* Action buttons for non-default lists */}\n            {list.id !== 'default' && (\n              <div\n                className=\"list-actions\"\n                style={{\n                  position: 'absolute',\n                  top: '4px',\n                  right: '4px',\n                  display: 'flex',\n                  gap: '2px'\n                }}\n              >\n                {/* Edit button */}\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    handleEditList(list);\n                  }}\n                  style={{\n                    width: '18px',\n                    height: '18px',\n                    border: 'none',\n                    borderRadius: '50%',\n                    backgroundColor: 'rgba(52, 152, 219, 0.8)',\n                    color: 'white',\n                    cursor: 'pointer',\n                    fontSize: '9px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    transition: 'all 0.2s'\n                  }}\n                  title={`Edit ${list.name} list`}\n                  onMouseEnter={(e) => {\n                    e.target.style.backgroundColor = '#3498db';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.target.style.backgroundColor = 'rgba(52, 152, 219, 0.8)';\n                  }}\n                >\n                  ✎\n                </button>\n\n                {/* Delete button */}\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    handleDeleteList(list.id);\n                  }}\n                  style={{\n                    width: '18px',\n                    height: '18px',\n                    border: 'none',\n                    borderRadius: '50%',\n                    backgroundColor: 'rgba(220, 53, 69, 0.8)',\n                    color: 'white',\n                    cursor: 'pointer',\n                    fontSize: '11px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    transition: 'all 0.2s'\n                  }}\n                  title={`Delete ${list.name} list`}\n                  onMouseEnter={(e) => {\n                    e.target.style.backgroundColor = '#dc3545';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.target.style.backgroundColor = 'rgba(220, 53, 69, 0.8)';\n                  }}\n                >\n                  ×\n                </button>\n              </div>\n            )}\n          </div>\n        ))}\n        <button\n          onClick={() => setShowListForm(true)}\n          style={{\n            padding: '0.5rem 1rem',\n            border: '2px dashed #dee2e6',\n            borderRadius: '6px',\n            backgroundColor: 'transparent',\n            color: '#6c757d',\n            cursor: 'pointer',\n            transition: 'all 0.2s',\n            fontSize: '0.9rem',\n            whiteSpace: 'nowrap'\n          }}\n        >\n          + Add List\n        </button>\n        </div>\n      </div>\n\n      <div className=\"dashboard-header\">\n        <h2 style={{ color: currentList.color }}>\n          {currentList.name} ({contacts.length})\n        </h2>\n        <div>\n          <button\n            className=\"btn btn-success\"\n            onClick={() => setShowContactForm(true)}\n            style={{ marginRight: '1rem' }}\n          >\n            Add Contact\n          </button>\n          <button\n            className=\"btn btn-primary\"\n            onClick={() => setShowCSVImport(true)}\n          >\n            Import CSV\n          </button>\n        </div>\n      </div>\n\n      {contacts.length === 0 ? (\n        <div style={{ textAlign: 'center', padding: '3rem' }}>\n          <h3>No contacts yet</h3>\n          <p>Add your first contact or import from CSV to get started.</p>\n        </div>\n      ) : (\n        <div className=\"contact-list\">\n          {contacts.map((contact) => (\n            <div\n              key={contact.id}\n              className=\"contact-card\"\n              style={{ position: 'relative' }}\n            >\n              <div\n                onClick={() => handleContactClick(contact.id)}\n                style={{ cursor: 'pointer' }}\n              >\n                <div className=\"contact-name\">\n                  {contact.firstName && contact.lastName\n                    ? `${contact.firstName} ${contact.lastName}`\n                    : contact.name\n                  }\n                </div>\n                <div className=\"contact-email\">{contact.email}</div>\n\n                {/* Company and title */}\n                {(contact.company || contact.title) && (\n                  <div style={{ color: '#6c757d', marginBottom: '0.5rem', fontSize: '0.9rem' }}>\n                    {contact.title && contact.company\n                      ? `${contact.title} at ${contact.company}`\n                      : contact.title || contact.company\n                    }\n                  </div>\n                )}\n\n                {contact.phone && (\n                  <div style={{ color: '#6c757d', marginBottom: '0.5rem' }}>\n                    📞 {contact.phone}\n                  </div>\n                )}\n\n                {/* Social media links */}\n                {(contact.linkedinUrl || contact.website) && (\n                  <div style={{\n                    display: 'flex',\n                    gap: '0.5rem',\n                    marginBottom: '0.5rem',\n                    fontSize: '0.8rem'\n                  }}>\n                    {contact.linkedinUrl && (\n                      <a\n                        href={contact.linkedinUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        style={{ color: '#0077b5', textDecoration: 'none' }}\n                        onClick={(e) => e.stopPropagation()}\n                      >\n                        LinkedIn\n                      </a>\n                    )}\n                    {contact.website && (\n                      <a\n                        href={contact.website}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        style={{ color: '#3498db', textDecoration: 'none' }}\n                        onClick={(e) => e.stopPropagation()}\n                      >\n                        Website\n                      </a>\n                    )}\n                  </div>\n                )}\n\n                <div className={`contact-status ${contact.status}`}>\n                  {contact.status}\n                </div>\n                <div className=\"contact-meta\">\n                  <div>Conversations: {contact.conversationCount || 0}</div>\n                  <div>Last contact: {formatDate(contact.lastContactDate)}</div>\n                  <div>Added: {formatDate(contact.createdAt)}</div>\n                </div>\n              </div>\n\n              {/* Delete button */}\n              <button\n                onClick={(e) => {\n                  e.stopPropagation();\n                  handleDeleteContact(contact.id);\n                }}\n                style={{\n                  position: 'absolute',\n                  top: '0.5rem',\n                  right: '0.5rem',\n                  background: '#dc3545',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '4px',\n                  width: '24px',\n                  height: '24px',\n                  fontSize: '12px',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}\n                title=\"Delete contact\"\n              >\n                ×\n              </button>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {showContactForm && (\n        <ContactForm\n          onClose={() => setShowContactForm(false)}\n          onContactCreated={handleContactCreated}\n          defaultListId={selectedListId}\n          lists={lists}\n        />\n      )}\n\n      {showCSVImport && (\n        <CSVImport\n          onClose={() => setShowCSVImport(false)}\n          onImportComplete={handleCSVImported}\n          defaultListId={selectedListId}\n          lists={lists}\n        />\n      )}\n\n      {showListForm && (\n        <ListForm\n          onClose={() => {\n            setShowListForm(false);\n            setEditingList(null);\n          }}\n          onListCreated={handleListCreated}\n          list={editingList}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,EAAEC,OAAO,QAAQ,iBAAiB;AACrD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwB,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM8B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd8B,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN9B,SAAS,CAAC,MAAM;IACd,IAAIe,cAAc,EAAE;MAClBgB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAChB,cAAc,CAAC,CAAC,CAAC,CAAC;;EAEtB,MAAMe,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1DhC,OAAO,CAACiC,MAAM,CAAC,CAAC,EAChBlC,UAAU,CAACkC,MAAM,CAAC,CAAC,CACpB,CAAC;MACFxB,QAAQ,CAACoB,aAAa,CAACK,IAAI,CAAC;MAC5BvB,WAAW,CAACmB,gBAAgB,CAACI,IAAI,CAAC;MAClCjB,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOkB,GAAG,EAAE;MACZlB,QAAQ,CAACkB,GAAG,CAACC,OAAO,CAAC;IACvB,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMtC,UAAU,CAACkC,MAAM,CAAC,CAAC;MAC1C,MAAMK,gBAAgB,GAAGD,QAAQ,CAACH,IAAI,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK7B,cAAc,CAAC;MAC/ED,WAAW,CAAC2B,gBAAgB,CAAC;IAC/B,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZlB,QAAQ,CAACkB,GAAG,CAACC,OAAO,CAAC;IACvB;EACF,CAAC;EAED,MAAMM,kBAAkB,GAAIC,SAAS,IAAK;IACxCjB,QAAQ,CAAC,YAAYiB,SAAS,EAAE,CAAC;EACnC,CAAC;EAED,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IACjCzB,kBAAkB,CAAC,KAAK,CAAC;IACzBQ,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,MAAMkB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BxB,gBAAgB,CAAC,KAAK,CAAC;IACvBM,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,MAAMmB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BvB,eAAe,CAAC,KAAK,CAAC;IACtBE,cAAc,CAAC,IAAI,CAAC;IACpBE,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,MAAMoB,cAAc,GAAIC,IAAI,IAAK;IAC/BvB,cAAc,CAACuB,IAAI,CAAC;IACpBzB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM0B,gBAAgB,GAAG,MAAOR,MAAM,IAAK;IACzC,IAAIA,MAAM,KAAK,SAAS,EAAE;MACxBS,KAAK,CAAC,wCAAwC,CAAC;MAC/C;IACF;IAEA,MAAMF,IAAI,GAAGxC,KAAK,CAAC2C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKZ,MAAM,CAAC;IAC7C,MAAMa,YAAY,GAAG,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,YAAY,KAAI,CAAC;IAE5C,IAAIC,cAAc,GAAG,6CAA6CP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEQ,IAAI,IAAI;IAChF,IAAIF,YAAY,GAAG,CAAC,EAAE;MACpBC,cAAc,IAAI,0BAA0BD,YAAY,iEAAiE;IAC3H;IAEA,IAAIG,MAAM,CAACC,OAAO,CAACH,cAAc,CAAC,EAAE;MAClC,IAAI;QACF,MAAMlB,QAAQ,GAAG,MAAMrC,OAAO,CAAC2D,MAAM,CAAClB,MAAM,CAAC;;QAE7C;QACA,IAAIJ,QAAQ,CAACH,IAAI,CAAC0B,aAAa,GAAG,CAAC,EAAE;UACnCV,KAAK,CAAC,8BAA8Bb,QAAQ,CAACH,IAAI,CAAC0B,aAAa,+CAA+C,CAAC;QACjH;;QAEA;QACA,IAAIhD,cAAc,KAAK6B,MAAM,EAAE;UAC7B5B,iBAAiB,CAAC,SAAS,CAAC;QAC9B;QAEAc,QAAQ,CAAC,CAAC;MACZ,CAAC,CAAC,OAAOQ,GAAG,EAAE;QACZlB,QAAQ,CAACkB,GAAG,CAACC,OAAO,CAAC;QACrBc,KAAK,CAAC,yBAAyB,GAAGf,GAAG,CAACC,OAAO,CAAC;MAChD;IACF;EACF,CAAC;EAED,MAAMyB,mBAAmB,GAAG,MAAOlB,SAAS,IAAK;IAC/C,IAAIc,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAM3D,UAAU,CAAC4D,MAAM,CAAChB,SAAS,CAAC;QAClChB,QAAQ,CAAC,CAAC;MACZ,CAAC,CAAC,OAAOQ,GAAG,EAAE;QACZlB,QAAQ,CAACkB,GAAG,CAACC,OAAO,CAAC;MACvB;IACF;EACF,CAAC;EAED,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IAC3B,OAAOtD,KAAK,CAAC2C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKzC,cAAc,CAAC,IAAI;MAAE4C,IAAI,EAAE,cAAc;MAAEO,KAAK,EAAE;IAAU,CAAC;EAC/F,CAAC;EAED,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,OAAO;IAC/B,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC;EAClD,CAAC;EAED,IAAIrD,OAAO,EAAE;IACX,oBACET,OAAA;MAAK+D,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBhE,OAAA;QAAKiE,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAH,QAAA,EAAC;MAEtD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI5D,KAAK,EAAE;IACT,oBACEX,OAAA;MAAK+D,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBhE,OAAA;QAAKiE,KAAK,EAAE;UAAEC,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAET,KAAK,EAAE;QAAM,CAAE;QAAAM,QAAA,GAAC,SAC3D,EAACrD,KAAK,eACbX,OAAA;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvE,OAAA;UAAQ+D,SAAS,EAAC,iBAAiB;UAACS,OAAO,EAAEjD,YAAa;UAAC0C,KAAK,EAAE;YAAEQ,SAAS,EAAE;UAAO,CAAE;UAAAT,QAAA,EAAC;QAEzF;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAMG,WAAW,GAAGjB,cAAc,CAAC,CAAC;EAEpC,oBACEzD,OAAA;IAAK+D,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBhE,OAAA;MAAKiE,KAAK,EAAE;QACVU,YAAY,EAAE,MAAM;QACpBC,YAAY,EAAE,mBAAmB;QACjCC,aAAa,EAAE;MACjB,CAAE;MAAAb,QAAA,eACAhE,OAAA;QACE+D,SAAS,EAAC,iBAAiB;QAC3BE,KAAK,EAAE;UACLa,OAAO,EAAE,MAAM;UACfC,QAAQ,EAAE,MAAM;UAChBC,GAAG,EAAE,QAAQ;UACbC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE,MAAM;UACjBL,aAAa,EAAE;QACjB,CAAE;QAAAb,QAAA,GACH7D,KAAK,CAACgF,GAAG,CAAExC,IAAI,iBACd3C,OAAA;UAEEiE,KAAK,EAAE;YACLmB,QAAQ,EAAE,UAAU;YACpBN,OAAO,EAAE;UACX,CAAE;UACFf,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAEpBhE,OAAA;YACEwE,OAAO,EAAEA,CAAA,KAAMhE,iBAAiB,CAACmC,IAAI,CAACK,EAAE,CAAE;YAC1C,cAAY,IAAIL,IAAI,CAACM,YAAY,IAAI,CAAC,GAAI;YAC1CgB,KAAK,EAAE;cACLE,OAAO,EAAE,aAAa;cACtBkB,YAAY,EAAE1C,IAAI,CAACK,EAAE,KAAK,SAAS,GAAG,MAAM,GAAG,MAAM;cACrDsC,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBC,eAAe,EAAEjF,cAAc,KAAKoC,IAAI,CAACK,EAAE,GAAGL,IAAI,CAACe,KAAK,GAAG,SAAS;cACpEA,KAAK,EAAEnD,cAAc,KAAKoC,IAAI,CAACK,EAAE,GAAG,OAAO,GAAG,SAAS;cACvDyC,MAAM,EAAE,SAAS;cACjBC,UAAU,EAAEnF,cAAc,KAAKoC,IAAI,CAACK,EAAE,GAAG,MAAM,GAAG,QAAQ;cAC1D2C,UAAU,EAAE,UAAU;cACtBb,OAAO,EAAE,MAAM;cACfG,UAAU,EAAE,QAAQ;cACpBD,GAAG,EAAE,QAAQ;cACbY,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,QAAQ;cACpBC,QAAQ,EAAE,aAAa;cACvBC,QAAQ,EAAE;YACZ,CAAE;YAAA/B,QAAA,gBAEFhE,OAAA;cACEiE,KAAK,EAAE;gBACL+B,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdV,YAAY,EAAE,KAAK;gBACnBC,eAAe,EAAEjF,cAAc,KAAKoC,IAAI,CAACK,EAAE,GAAG,uBAAuB,GAAGL,IAAI,CAACe,KAAK;gBAClFwC,UAAU,EAAE;cACd;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFvE,OAAA;cAAMiE,KAAK,EAAE;gBAAEkC,QAAQ,EAAE,QAAQ;gBAAEC,YAAY,EAAE;cAAW,CAAE;cAAApC,QAAA,GAC3DrB,IAAI,CAACQ,IAAI,EAAC,IAAE,EAACR,IAAI,CAACM,YAAY,IAAI,CAAC,EAAC,GACvC;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAGR5B,IAAI,CAACK,EAAE,KAAK,SAAS,iBACpBhD,OAAA;YACE+D,SAAS,EAAC,cAAc;YACxBE,KAAK,EAAE;cACLmB,QAAQ,EAAE,UAAU;cACpBiB,GAAG,EAAE,KAAK;cACVC,KAAK,EAAE,KAAK;cACZxB,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE;YACP,CAAE;YAAAhB,QAAA,gBAGFhE,OAAA;cACEwE,OAAO,EAAG+B,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB9D,cAAc,CAACC,IAAI,CAAC;cACtB,CAAE;cACFsB,KAAK,EAAE;gBACL+B,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdX,MAAM,EAAE,MAAM;gBACdC,YAAY,EAAE,KAAK;gBACnBC,eAAe,EAAE,yBAAyB;gBAC1C9B,KAAK,EAAE,OAAO;gBACd+B,MAAM,EAAE,SAAS;gBACjBG,QAAQ,EAAE,KAAK;gBACfd,OAAO,EAAE,MAAM;gBACfG,UAAU,EAAE,QAAQ;gBACpBwB,cAAc,EAAE,QAAQ;gBACxBd,UAAU,EAAE;cACd,CAAE;cACFe,KAAK,EAAE,QAAQ/D,IAAI,CAACQ,IAAI,OAAQ;cAChCwD,YAAY,EAAGJ,CAAC,IAAK;gBACnBA,CAAC,CAACK,MAAM,CAAC3C,KAAK,CAACuB,eAAe,GAAG,SAAS;cAC5C,CAAE;cACFqB,YAAY,EAAGN,CAAC,IAAK;gBACnBA,CAAC,CAACK,MAAM,CAAC3C,KAAK,CAACuB,eAAe,GAAG,yBAAyB;cAC5D,CAAE;cAAAxB,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAGTvE,OAAA;cACEwE,OAAO,EAAG+B,CAAC,IAAK;gBACdA,CAAC,CAACC,eAAe,CAAC,CAAC;gBACnB5D,gBAAgB,CAACD,IAAI,CAACK,EAAE,CAAC;cAC3B,CAAE;cACFiB,KAAK,EAAE;gBACL+B,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdX,MAAM,EAAE,MAAM;gBACdC,YAAY,EAAE,KAAK;gBACnBC,eAAe,EAAE,wBAAwB;gBACzC9B,KAAK,EAAE,OAAO;gBACd+B,MAAM,EAAE,SAAS;gBACjBG,QAAQ,EAAE,MAAM;gBAChBd,OAAO,EAAE,MAAM;gBACfG,UAAU,EAAE,QAAQ;gBACpBwB,cAAc,EAAE,QAAQ;gBACxBd,UAAU,EAAE;cACd,CAAE;cACFe,KAAK,EAAE,UAAU/D,IAAI,CAACQ,IAAI,OAAQ;cAClCwD,YAAY,EAAGJ,CAAC,IAAK;gBACnBA,CAAC,CAACK,MAAM,CAAC3C,KAAK,CAACuB,eAAe,GAAG,SAAS;cAC5C,CAAE;cACFqB,YAAY,EAAGN,CAAC,IAAK;gBACnBA,CAAC,CAACK,MAAM,CAAC3C,KAAK,CAACuB,eAAe,GAAG,wBAAwB;cAC3D,CAAE;cAAAxB,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN;QAAA,GArHI5B,IAAI,CAACK,EAAE;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsHT,CACN,CAAC,eACFvE,OAAA;UACEwE,OAAO,EAAEA,CAAA,KAAMtD,eAAe,CAAC,IAAI,CAAE;UACrC+C,KAAK,EAAE;YACLE,OAAO,EAAE,aAAa;YACtBmB,MAAM,EAAE,oBAAoB;YAC5BC,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE,aAAa;YAC9B9B,KAAK,EAAE,SAAS;YAChB+B,MAAM,EAAE,SAAS;YACjBE,UAAU,EAAE,UAAU;YACtBC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE;UACd,CAAE;UAAA7B,QAAA,EACH;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvE,OAAA;MAAK+D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BhE,OAAA;QAAIiE,KAAK,EAAE;UAAEP,KAAK,EAAEgB,WAAW,CAAChB;QAAM,CAAE;QAAAM,QAAA,GACrCU,WAAW,CAACvB,IAAI,EAAC,IAAE,EAAC9C,QAAQ,CAACyG,MAAM,EAAC,GACvC;MAAA;QAAA1C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLvE,OAAA;QAAAgE,QAAA,gBACEhE,OAAA;UACE+D,SAAS,EAAC,iBAAiB;UAC3BS,OAAO,EAAEA,CAAA,KAAM1D,kBAAkB,CAAC,IAAI,CAAE;UACxCmD,KAAK,EAAE;YAAE8C,WAAW,EAAE;UAAO,CAAE;UAAA/C,QAAA,EAChC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTvE,OAAA;UACE+D,SAAS,EAAC,iBAAiB;UAC3BS,OAAO,EAAEA,CAAA,KAAMxD,gBAAgB,CAAC,IAAI,CAAE;UAAAgD,QAAA,EACvC;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELlE,QAAQ,CAACyG,MAAM,KAAK,CAAC,gBACpB9G,OAAA;MAAKiE,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAH,QAAA,gBACnDhE,OAAA;QAAAgE,QAAA,EAAI;MAAe;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBvE,OAAA;QAAAgE,QAAA,EAAG;MAAyD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC,gBAENvE,OAAA;MAAK+D,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1B3D,QAAQ,CAAC8E,GAAG,CAAE6B,OAAO,iBACpBhH,OAAA;QAEE+D,SAAS,EAAC,cAAc;QACxBE,KAAK,EAAE;UAAEmB,QAAQ,EAAE;QAAW,CAAE;QAAApB,QAAA,gBAEhChE,OAAA;UACEwE,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAC2E,OAAO,CAAChE,EAAE,CAAE;UAC9CiB,KAAK,EAAE;YAAEwB,MAAM,EAAE;UAAU,CAAE;UAAAzB,QAAA,gBAE7BhE,OAAA;YAAK+D,SAAS,EAAC,cAAc;YAAAC,QAAA,EAC1BgD,OAAO,CAACC,SAAS,IAAID,OAAO,CAACE,QAAQ,GAClC,GAAGF,OAAO,CAACC,SAAS,IAAID,OAAO,CAACE,QAAQ,EAAE,GAC1CF,OAAO,CAAC7D;UAAI;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CAAC,eACNvE,OAAA;YAAK+D,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEgD,OAAO,CAACG;UAAK;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAGnD,CAACyC,OAAO,CAACI,OAAO,IAAIJ,OAAO,CAACN,KAAK,kBAChC1G,OAAA;YAAKiE,KAAK,EAAE;cAAEP,KAAK,EAAE,SAAS;cAAEiB,YAAY,EAAE,QAAQ;cAAEiB,QAAQ,EAAE;YAAS,CAAE;YAAA5B,QAAA,EAC1EgD,OAAO,CAACN,KAAK,IAAIM,OAAO,CAACI,OAAO,GAC7B,GAAGJ,OAAO,CAACN,KAAK,OAAOM,OAAO,CAACI,OAAO,EAAE,GACxCJ,OAAO,CAACN,KAAK,IAAIM,OAAO,CAACI;UAAO;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEjC,CACN,EAEAyC,OAAO,CAACK,KAAK,iBACZrH,OAAA;YAAKiE,KAAK,EAAE;cAAEP,KAAK,EAAE,SAAS;cAAEiB,YAAY,EAAE;YAAS,CAAE;YAAAX,QAAA,GAAC,eACrD,EAACgD,OAAO,CAACK,KAAK;UAAA;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CACN,EAGA,CAACyC,OAAO,CAACM,WAAW,IAAIN,OAAO,CAACO,OAAO,kBACtCvH,OAAA;YAAKiE,KAAK,EAAE;cACVa,OAAO,EAAE,MAAM;cACfE,GAAG,EAAE,QAAQ;cACbL,YAAY,EAAE,QAAQ;cACtBiB,QAAQ,EAAE;YACZ,CAAE;YAAA5B,QAAA,GACCgD,OAAO,CAACM,WAAW,iBAClBtH,OAAA;cACEwH,IAAI,EAAER,OAAO,CAACM,WAAY;cAC1BV,MAAM,EAAC,QAAQ;cACfa,GAAG,EAAC,qBAAqB;cACzBxD,KAAK,EAAE;gBAAEP,KAAK,EAAE,SAAS;gBAAEgE,cAAc,EAAE;cAAO,CAAE;cACpDlD,OAAO,EAAG+B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;cAAAxC,QAAA,EACrC;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ,EACAyC,OAAO,CAACO,OAAO,iBACdvH,OAAA;cACEwH,IAAI,EAAER,OAAO,CAACO,OAAQ;cACtBX,MAAM,EAAC,QAAQ;cACfa,GAAG,EAAC,qBAAqB;cACzBxD,KAAK,EAAE;gBAAEP,KAAK,EAAE,SAAS;gBAAEgE,cAAc,EAAE;cAAO,CAAE;cACpDlD,OAAO,EAAG+B,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;cAAAxC,QAAA,EACrC;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,eAEDvE,OAAA;YAAK+D,SAAS,EAAE,kBAAkBiD,OAAO,CAACW,MAAM,EAAG;YAAA3D,QAAA,EAChDgD,OAAO,CAACW;UAAM;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACNvE,OAAA;YAAK+D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BhE,OAAA;cAAAgE,QAAA,GAAK,iBAAe,EAACgD,OAAO,CAACY,iBAAiB,IAAI,CAAC;YAAA;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1DvE,OAAA;cAAAgE,QAAA,GAAK,gBAAc,EAACL,UAAU,CAACqD,OAAO,CAACa,eAAe,CAAC;YAAA;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DvE,OAAA;cAAAgE,QAAA,GAAK,SAAO,EAACL,UAAU,CAACqD,OAAO,CAACc,SAAS,CAAC;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNvE,OAAA;UACEwE,OAAO,EAAG+B,CAAC,IAAK;YACdA,CAAC,CAACC,eAAe,CAAC,CAAC;YACnBhD,mBAAmB,CAACwD,OAAO,CAAChE,EAAE,CAAC;UACjC,CAAE;UACFiB,KAAK,EAAE;YACLmB,QAAQ,EAAE,UAAU;YACpBiB,GAAG,EAAE,QAAQ;YACbC,KAAK,EAAE,QAAQ;YACfyB,UAAU,EAAE,SAAS;YACrBrE,KAAK,EAAE,OAAO;YACd4B,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBS,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdL,QAAQ,EAAE,MAAM;YAChBH,MAAM,EAAE,SAAS;YACjBX,OAAO,EAAE,MAAM;YACfG,UAAU,EAAE,QAAQ;YACpBwB,cAAc,EAAE;UAClB,CAAE;UACFC,KAAK,EAAC,gBAAgB;UAAA1C,QAAA,EACvB;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,GApGJyC,OAAO,CAAChE,EAAE;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqGZ,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,EAEA1D,eAAe,iBACdb,OAAA,CAACJ,WAAW;MACVoI,OAAO,EAAEA,CAAA,KAAMlH,kBAAkB,CAAC,KAAK,CAAE;MACzCmH,gBAAgB,EAAE1F,oBAAqB;MACvC2F,aAAa,EAAE3H,cAAe;MAC9BJ,KAAK,EAAEA;IAAM;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACF,EAEAxD,aAAa,iBACZf,OAAA,CAACH,SAAS;MACRmI,OAAO,EAAEA,CAAA,KAAMhH,gBAAgB,CAAC,KAAK,CAAE;MACvCmH,gBAAgB,EAAE3F,iBAAkB;MACpC0F,aAAa,EAAE3H,cAAe;MAC9BJ,KAAK,EAAEA;IAAM;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACF,EAEAtD,YAAY,iBACXjB,OAAA,CAACF,QAAQ;MACPkI,OAAO,EAAEA,CAAA,KAAM;QACb9G,eAAe,CAAC,KAAK,CAAC;QACtBE,cAAc,CAAC,IAAI,CAAC;MACtB,CAAE;MACFgH,aAAa,EAAE3F,iBAAkB;MACjCE,IAAI,EAAExB;IAAY;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrE,EAAA,CAjeID,SAAS;EAAA,QAUIR,WAAW;AAAA;AAAA4I,EAAA,GAVxBpI,SAAS;AAmef,eAAeA,SAAS;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}