{"name": "email-dashboard", "version": "1.0.0", "description": "Email dashboard with contact management and conversation tracking", "main": "server/index.js", "scripts": {"dev": "concurrently --kill-others --prefix \"[{name}]\" --names \"SERVER,CLIENT\" \"npm run server\" \"npm run client\"", "start-dashboard": "node start-dashboard.js", "server": "cd server && npm run dev", "client": "cd client && npm start", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "build": "cd client && npm run build", "start": "cd server && npm start", "quick-start": "start-dashboard.bat"}, "keywords": ["email", "dashboard", "contacts", "conversations"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}