import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Contact API
export const contactAPI = {
  // Get all contacts
  getAll: () => api.get('/contacts'),
  
  // Get specific contact
  getById: (id) => api.get(`/contacts/${id}`),
  
  // Create new contact
  create: (contactData) => api.post('/contacts', contactData),
  
  // Update contact
  update: (id, contactData) => api.put(`/contacts/${id}`, contactData),
  
  // Delete contact
  delete: (id) => api.delete(`/contacts/${id}`),
};

// Conversation API
export const conversationAPI = {
  // Get all conversations
  getAll: () => api.get('/conversations'),
  
  // Get conversations for specific contact
  getByContactId: (contactId) => api.get(`/conversations/contact/${contactId}`),
  
  // Get specific conversation
  getById: (id) => api.get(`/conversations/${id}`),
  
  // Create new conversation
  create: (conversationData) => api.post('/conversations', conversationData),
  
  // Update conversation
  update: (id, conversationData) => api.put(`/conversations/${id}`, conversationData),
  
  // Delete conversation
  delete: (id) => api.delete(`/conversations/${id}`),
};

// Lists API
export const listAPI = {
  // Get all contact lists
  getAll: () => api.get('/lists'),

  // Get specific list
  getById: (id) => api.get(`/lists/${id}`),

  // Get contacts in a specific list
  getContacts: (id) => api.get(`/lists/${id}/contacts`),

  // Create new list
  create: (listData) => api.post('/lists', listData),

  // Update list
  update: (id, listData) => api.put(`/lists/${id}`, listData),

  // Delete list
  delete: (id) => api.delete(`/lists/${id}`),

  // Update contact count for a list
  updateCount: (id) => api.post(`/lists/${id}/update-count`),
};

// Import API
export const importAPI = {
  // Analyze CSV structure and get column mapping suggestions
  analyzeCSV: (file) => {
    const formData = new FormData();
    formData.append('csvFile', file);

    return api.post('/import/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Import contacts from CSV
  uploadCSV: (file, listId = 'default', customMapping = null) => {
    const formData = new FormData();
    formData.append('csvFile', file);
    formData.append('listId', listId);
    if (customMapping) {
      formData.append('mapping', JSON.stringify(customMapping));
    }

    return api.post('/import/csv', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Preview CSV data before importing
  previewCSV: (file, customMapping = null) => {
    const formData = new FormData();
    formData.append('csvFile', file);
    if (customMapping) {
      formData.append('mapping', JSON.stringify(customMapping));
    }

    return api.post('/import/preview', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Download CSV template
  downloadTemplate: () => {
    return api.get('/import/template', {
      responseType: 'blob',
    });
  },
};

// Error handling interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.error || 'An error occurred';
      throw new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('Unable to connect to server');
    } else {
      // Something else happened
      throw new Error('An unexpected error occurred');
    }
  }
);

export default api;
