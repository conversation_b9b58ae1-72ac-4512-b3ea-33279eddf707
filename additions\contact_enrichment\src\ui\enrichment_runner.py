#!/usr/bin/env python3
"""
Interactive runner for the Lead Enrichment Tool
Provides a user-friendly interface to run enrichment on selected CSV files
"""

import asyncio
import os
import sys
from datetime import datetime
import pandas as pd
from dotenv import load_dotenv
from lead_enrichment import LeadEnrichmentTool
from sales_email_generator import SalesEmailGenerator
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnrichmentRunner:
    """Interactive runner for lead enrichment"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.apify_api_key = os.getenv('APIFY_API_KEY')

        if not self.openai_api_key:
            print("❌ Error: OpenAI API key not found in .env file")
            print("Please add OPENAI_API_KEY to your .env file")
            sys.exit(1)

        if self.apify_api_key:
            print("✅ LinkedIn enrichment enabled (Apify API key found)")
        else:
            print("⚠️ LinkedIn enrichment disabled (no Apify API key found)")

        self.csv_files = []
        self.load_csv_files()
    
    def load_csv_files(self):
        """Load available CSV files"""
        # Check both current directory and data subdirectory
        csv_files = []

        # Current directory
        for f in os.listdir('.'):
            if f.endswith('.csv') and not f.endswith('_enriched.csv'):
                csv_files.append(f)

        # Data subdirectory
        if os.path.exists('data'):
            for f in os.listdir('data'):
                if f.endswith('.csv') and not f.endswith('_enriched.csv'):
                    csv_files.append(os.path.join('data', f))

        self.csv_files = sorted(csv_files)
    
    def display_csv_info(self):
        """Display information about available CSV files"""
        print("\n" + "="*80)
        print("SALES LEAD DATABASE - ENRICHMENT TOOL")
        print("✅ Website Scraping + AI Analysis")
        print("✅ LinkedIn Profile Enrichment")
        print("="*80)
        print(f"Found {len(self.csv_files)} CSV files ready for enrichment:\n")
        
        total_leads = 0
        for i, file in enumerate(self.csv_files):
            try:
                df = pd.read_csv(file)
                leads_count = len(df)
                total_leads += leads_count
                
                # Check if already enriched
                enriched_count = 0
                if 'ai_company_summary' in df.columns:
                    enriched_count = df['ai_company_summary'].notna().sum()
                
                # Check website URL coverage
                website_count = 0
                if 'website_url' in df.columns:
                    website_count = df['website_url'].notna().sum()

                # Check LinkedIn URL coverage
                linkedin_count = 0
                if 'linkedin_url' in df.columns:
                    linkedin_count = df['linkedin_url'].notna().sum()

                status = "✓ Ready" if enriched_count == 0 else f"⚡ {enriched_count}/{leads_count} enriched"

                print(f"{i+1:2d}. {file:<40} | {leads_count:4d} leads | {website_count:4d} URLs | {linkedin_count:4d} LI | {status}")
                
            except Exception as e:
                print(f"{i+1:2d}. {file:<40} | ERROR: {str(e)}")
        
        print(f"\nTotal leads in database: {total_leads:,}")
        print("="*80)
    
    def get_user_choice(self):
        """Get user's choice for which files to process"""
        while True:
            print("\nOptions:")
            print("1. Run test enrichment (5 leads from first file)")
            print("2. Enrich specific file")
            print("3. Enrich all files")
            print("4. Resume enrichment (continue from where left off)")
            print("5. View enrichment results")
            print("6. Generate sales emails for enriched leads")
            print("7. Exit")

            choice = input("\nEnter your choice (1-7): ").strip()

            if choice in ['1', '2', '3', '4', '5', '6', '7']:
                return choice
            else:
                print("Invalid choice. Please enter 1-7.")
    
    def select_file(self):
        """Let user select a specific file"""
        while True:
            try:
                file_num = int(input(f"\nEnter file number (1-{len(self.csv_files)}): "))
                if 1 <= file_num <= len(self.csv_files):
                    return self.csv_files[file_num - 1]
                else:
                    print(f"Please enter a number between 1 and {len(self.csv_files)}")
            except ValueError:
                print("Please enter a valid number")
    
    def get_processing_params(self):
        """Get processing parameters from user"""
        print("\nProcessing options:")
        
        # Max leads
        max_leads_input = input("Max leads to process (press Enter for all): ").strip()
        max_leads = None
        if max_leads_input:
            try:
                max_leads = int(max_leads_input)
            except ValueError:
                print("Invalid number, processing all leads")
        
        # Start from
        start_from_input = input("Start from lead number (press Enter for 0): ").strip()
        start_from = 0
        if start_from_input:
            try:
                start_from = int(start_from_input)
            except ValueError:
                print("Invalid number, starting from 0")
        
        return max_leads, start_from
    
    async def run_enrichment(self, files_to_process, max_leads=None, start_from=0):
        """Run the enrichment process"""
        if not self.openai_api_key:
            print("❌ Error: OpenAI API key not found")
            return

        enricher = LeadEnrichmentTool(self.openai_api_key, self.apify_api_key)
        
        try:
            await enricher.initialize_crawler()
            
            for file in files_to_process:
                print(f"\n{'='*60}")
                print(f"Processing: {file}")
                print(f"{'='*60}")
                
                await enricher.process_csv_file(file, max_leads, start_from)
                
                print(f"\nCompleted {file}")
                print(f"Total processed: {enricher.processed_count}")
                print(f"Successful: {enricher.success_count}")
                print(f"Errors: {enricher.error_count}")
        
        except Exception as e:
            logger.error(f"Error during enrichment: {e}")
        finally:
            await enricher.cleanup_crawler()
    
    def view_results(self):
        """View enrichment results"""
        enriched_files = [f for f in os.listdir('.') if f.endswith('_enriched.csv')]
        
        if not enriched_files:
            print("\nNo enriched files found yet.")
            return
        
        print(f"\nFound {len(enriched_files)} enriched files:")
        for i, file in enumerate(enriched_files):
            try:
                df = pd.read_csv(file)
                enriched_count = 0
                if 'ai_company_summary' in df.columns:
                    enriched_count = df['ai_company_summary'].notna().sum()
                
                print(f"{i+1}. {file} - {len(df)} total leads, {enriched_count} enriched")
            except Exception as e:
                print(f"{i+1}. {file} - Error reading file: {e}")
        
        # Show sample of enriched data
        if enriched_files:
            try:
                sample_file = enriched_files[0]
                df = pd.read_csv(sample_file)
                
                # Find first enriched row
                enriched_rows = df[df['ai_company_summary'].notna() & (df['ai_company_summary'] != '')]
                if not enriched_rows.empty:
                    sample_row = enriched_rows.iloc[0]
                    print(f"\nSample enriched data from {sample_file}:")
                    print(f"Company: {sample_row.get('company', 'N/A')}")
                    print(f"Summary: {sample_row.get('ai_company_summary', 'N/A')[:200]}...")
                    print(f"Sales Opportunities: {sample_row.get('ai_sales_opportunities', 'N/A')[:200]}...")
            except Exception as e:
                print(f"Error showing sample: {e}")

    def generate_sales_emails(self):
        """Generate sales emails for enriched leads"""
        enriched_files = [f for f in os.listdir('.') if f.endswith('_enriched.csv')]

        # Also check data directory
        if os.path.exists('data'):
            for f in os.listdir('data'):
                if f.endswith('_enriched.csv'):
                    enriched_files.append(os.path.join('data', f))

        if not enriched_files:
            print("\nNo enriched files found. Please run enrichment first.")
            return

        print(f"\nFound {len(enriched_files)} enriched files:")
        for i, file in enumerate(enriched_files):
            try:
                df = pd.read_csv(file)
                enriched_count = 0
                if 'ai_company_summary' in df.columns:
                    enriched_count = df[df['ai_company_summary'].notna() & (df['ai_company_summary'] != '')].shape[0]

                print(f"{i+1}. {file} - {len(df)} total leads, {enriched_count} enriched")
            except Exception as e:
                print(f"{i+1}. {file} - Error reading file: {e}")

        # Select file
        while True:
            try:
                file_num = int(input(f"\nSelect file for email generation (1-{len(enriched_files)}): "))
                if 1 <= file_num <= len(enriched_files):
                    selected_file = enriched_files[file_num - 1]
                    break
                else:
                    print(f"Please enter a number between 1 and {len(enriched_files)}")
            except ValueError:
                print("Please enter a valid number")

        # Load the file
        try:
            df = pd.read_csv(selected_file)
            print(f"\nLoaded {len(df)} leads from {selected_file}")

            # Filter to only enriched leads
            enriched_leads = df[df['ai_company_summary'].notna() & (df['ai_company_summary'] != '')]
            print(f"Found {len(enriched_leads)} enriched leads ready for email generation")

            if len(enriched_leads) == 0:
                print("No enriched leads found in this file.")
                return

            # Get number of emails to generate
            max_emails_input = input(f"How many emails to generate (max {len(enriched_leads)}, press Enter for all): ").strip()
            if max_emails_input:
                try:
                    max_emails = min(int(max_emails_input), len(enriched_leads))
                except ValueError:
                    max_emails = len(enriched_leads)
            else:
                max_emails = len(enriched_leads)

            print(f"\nGenerating {max_emails} sales emails...")

            # Initialize email generator
            if not self.openai_api_key:
                print("❌ Error: OpenAI API key not found")
                return

            email_generator = SalesEmailGenerator(self.openai_api_key)

            # Generate emails
            emails_data = []
            for i, (idx, row) in enumerate(enriched_leads.head(max_emails).iterrows()):
                try:
                    print(f"Generating email {i+1}/{max_emails} for {row.get('company', 'Unknown Company')}...")
                    email_data = email_generator.generate_email_for_lead(row)

                    # Add row index for reference
                    email_data['row_index'] = str(idx)
                    emails_data.append(email_data)

                except Exception as e:
                    print(f"Error generating email for {row.get('company', 'Unknown')}: {e}")
                    continue

            # Save emails to file
            if emails_data:
                emails_df = pd.DataFrame(emails_data)
                output_file = selected_file.replace('_enriched.csv', '_sales_emails.csv')
                emails_df.to_csv(output_file, index=False)

                print(f"\n✅ Generated {len(emails_data)} sales emails!")
                print(f"📧 Emails saved to: {output_file}")

                # Show sample email
                if emails_data:
                    sample = emails_data[0]
                    print(f"\nSample Email for {sample.get('lead_company', 'Unknown')}:")
                    print("-" * 60)
                    print(f"Subject: {sample.get('subject', 'N/A')}")
                    print(f"\nEmail Preview:\n{sample.get('email_body', 'N/A')[:300]}...")
                    print(f"\nDeals Offered: {sample.get('deals_offered', 'N/A')}")
            else:
                print("No emails were generated successfully.")

        except Exception as e:
            print(f"Error processing file: {e}")
    
    async def run(self):
        """Main run loop"""
        while True:
            self.load_csv_files()
            self.display_csv_info()
            
            choice = self.get_user_choice()
            
            if choice == '1':
                # Test run
                if self.csv_files:
                    print(f"\nRunning test enrichment on {self.csv_files[0]} (first 5 leads)")
                    await self.run_enrichment([self.csv_files[0]], max_leads=5, start_from=0)
                else:
                    print("No CSV files found!")
            
            elif choice == '2':
                # Specific file
                if self.csv_files:
                    selected_file = self.select_file()
                    max_leads, start_from = self.get_processing_params()
                    await self.run_enrichment([selected_file], max_leads, start_from)
                else:
                    print("No CSV files found!")
            
            elif choice == '3':
                # All files
                if self.csv_files:
                    max_leads, start_from = self.get_processing_params()
                    await self.run_enrichment(self.csv_files, max_leads, start_from)
                else:
                    print("No CSV files found!")
            
            elif choice == '4':
                # Resume enrichment
                print("Resume functionality - will continue from where enrichment left off")
                if self.csv_files:
                    selected_file = self.select_file()
                    await self.run_enrichment([selected_file], max_leads=None, start_from=0)
                else:
                    print("No CSV files found!")
            
            elif choice == '5':
                # View results
                self.view_results()

            elif choice == '6':
                # Generate sales emails
                self.generate_sales_emails()

            elif choice == '7':
                # Exit
                print("Goodbye!")
                break
            
            input("\nPress Enter to continue...")

async def main():
    """Main entry point"""
    runner = EnrichmentRunner()
    await runner.run()

if __name__ == "__main__":
    asyncio.run(main())
