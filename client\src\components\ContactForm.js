import React, { useState } from 'react';
import { contactAPI } from '../services/api';

const ContactForm = ({ onClose, onContactCreated, contact = null, defaultListId = 'default', lists = [] }) => {
  const [formData, setFormData] = useState({
    firstName: contact?.firstName || '',
    lastName: contact?.lastName || '',
    name: contact?.name || '',
    email: contact?.email || '',
    phone: contact?.phone || '',
    status: contact?.status || 'active',
    listId: contact?.listId || defaultListId,

    // Company information
    company: contact?.company || '',
    companyType: contact?.companyType || '',
    title: contact?.title || '',

    // URLs and social media
    website: contact?.website || '',
    linkedinUrl: contact?.linkedinUrl || '',
    instagramUrl: contact?.instagramUrl || '',
    facebookUrl: contact?.facebookUrl || '',

    // Address information
    address: contact?.address || '',
    city: contact?.city || '',
    state: contact?.state || '',
    zip: contact?.zip || '',
    country: contact?.country || '',

    // Additional fields
    notes: contact?.notes || ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Auto-generate name from first/last name if not provided
    const contactData = { ...formData };
    if (!contactData.name.trim() && (contactData.firstName.trim() || contactData.lastName.trim())) {
      contactData.name = `${contactData.firstName} ${contactData.lastName}`.trim();
    }

    if (!contactData.name.trim() || !contactData.email.trim()) {
      setError('Name (or first/last name) and email are required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      if (contact) {
        await contactAPI.update(contact.id, contactData);
      } else {
        await contactAPI.create(contactData);
      }
      
      onContactCreated();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        width: '90%',
        maxWidth: '500px',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        <h3>{contact ? 'Edit Contact' : 'Add New Contact'}</h3>
        
        {error && (
          <div style={{
            backgroundColor: '#f8d7da',
            color: '#721c24',
            padding: '0.75rem',
            borderRadius: '4px',
            marginBottom: '1rem'
          }}>
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div className="form-group">
              <label htmlFor="firstName">First Name</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                className="form-control"
                value={formData.firstName}
                onChange={handleChange}
              />
            </div>
            <div className="form-group">
              <label htmlFor="lastName">Last Name</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                className="form-control"
                value={formData.lastName}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="name">Full Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              className="form-control"
              value={formData.name}
              onChange={handleChange}
              placeholder="Auto-filled from first/last name, or enter manually"
            />
            <small style={{ color: '#6c757d', fontSize: '0.8rem' }}>
              Will be auto-generated from first/last name if left empty
            </small>
          </div>

          <div className="form-group">
            <label htmlFor="email">Email *</label>
            <input
              type="email"
              id="email"
              name="email"
              className="form-control"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="phone">Phone</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              className="form-control"
              value={formData.phone}
              onChange={handleChange}
            />
          </div>

          {/* Company Information Section */}
          <div style={{ marginTop: '2rem', marginBottom: '1rem' }}>
            <h4 style={{ color: '#495057', fontSize: '1.1rem', marginBottom: '1rem' }}>
              Company Information
            </h4>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div className="form-group">
              <label htmlFor="company">Company</label>
              <input
                type="text"
                id="company"
                name="company"
                className="form-control"
                value={formData.company}
                onChange={handleChange}
                placeholder="Company name"
              />
            </div>
            <div className="form-group">
              <label htmlFor="companyType">Company Type</label>
              <select
                id="companyType"
                name="companyType"
                className="form-control"
                value={formData.companyType}
                onChange={handleChange}
              >
                <option value="">Select type</option>
                <option value="startup">Startup</option>
                <option value="small_business">Small Business</option>
                <option value="enterprise">Enterprise</option>
                <option value="nonprofit">Nonprofit</option>
                <option value="government">Government</option>
                <option value="education">Education</option>
                <option value="healthcare">Healthcare</option>
                <option value="technology">Technology</option>
                <option value="consulting">Consulting</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="title">Job Title</label>
            <input
              type="text"
              id="title"
              name="title"
              className="form-control"
              value={formData.title}
              onChange={handleChange}
              placeholder="Job title or position"
            />
          </div>

          {/* URLs and Social Media Section */}
          <div style={{ marginTop: '2rem', marginBottom: '1rem' }}>
            <h4 style={{ color: '#495057', fontSize: '1.1rem', marginBottom: '1rem' }}>
              URLs & Social Media
            </h4>
          </div>

          <div className="form-group">
            <label htmlFor="website">Website</label>
            <input
              type="url"
              id="website"
              name="website"
              className="form-control"
              value={formData.website}
              onChange={handleChange}
              placeholder="https://example.com"
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div className="form-group">
              <label htmlFor="linkedinUrl">LinkedIn</label>
              <input
                type="url"
                id="linkedinUrl"
                name="linkedinUrl"
                className="form-control"
                value={formData.linkedinUrl}
                onChange={handleChange}
                placeholder="https://linkedin.com/in/username"
              />
            </div>
            <div className="form-group">
              <label htmlFor="instagramUrl">Instagram</label>
              <input
                type="url"
                id="instagramUrl"
                name="instagramUrl"
                className="form-control"
                value={formData.instagramUrl}
                onChange={handleChange}
                placeholder="https://instagram.com/username"
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="facebookUrl">Facebook</label>
            <input
              type="url"
              id="facebookUrl"
              name="facebookUrl"
              className="form-control"
              value={formData.facebookUrl}
              onChange={handleChange}
              placeholder="https://facebook.com/username"
            />
          </div>

          {/* Address Section */}
          <div style={{ marginTop: '2rem', marginBottom: '1rem' }}>
            <h4 style={{ color: '#495057', fontSize: '1.1rem', marginBottom: '1rem' }}>
              Address Information
            </h4>
          </div>

          <div className="form-group">
            <label htmlFor="address">Address</label>
            <input
              type="text"
              id="address"
              name="address"
              className="form-control"
              value={formData.address}
              onChange={handleChange}
              placeholder="Street address"
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '1rem' }}>
            <div className="form-group">
              <label htmlFor="city">City</label>
              <input
                type="text"
                id="city"
                name="city"
                className="form-control"
                value={formData.city}
                onChange={handleChange}
              />
            </div>
            <div className="form-group">
              <label htmlFor="state">State/Province</label>
              <input
                type="text"
                id="state"
                name="state"
                className="form-control"
                value={formData.state}
                onChange={handleChange}
              />
            </div>
            <div className="form-group">
              <label htmlFor="zip">ZIP/Postal Code</label>
              <input
                type="text"
                id="zip"
                name="zip"
                className="form-control"
                value={formData.zip}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="form-group">
            <label htmlFor="country">Country</label>
            <input
              type="text"
              id="country"
              name="country"
              className="form-control"
              value={formData.country}
              onChange={handleChange}
            />
          </div>

          {/* Additional Information Section */}
          <div style={{ marginTop: '2rem', marginBottom: '1rem' }}>
            <h4 style={{ color: '#495057', fontSize: '1.1rem', marginBottom: '1rem' }}>
              Additional Information
            </h4>
          </div>

          <div className="form-group">
            <label htmlFor="notes">Notes</label>
            <textarea
              id="notes"
              name="notes"
              className="form-control"
              value={formData.notes}
              onChange={handleChange}
              rows="3"
              placeholder="Additional notes or comments"
              style={{ resize: 'vertical' }}
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div className="form-group">
              <label htmlFor="status">Status</label>
              <select
                id="status"
                name="status"
                className="form-control"
                value={formData.status}
                onChange={handleChange}
              >
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="listId">Contact List</label>
              <select
                id="listId"
                name="listId"
                className="form-control"
                value={formData.listId}
                onChange={handleChange}
              >
                {lists.map((list) => (
                  <option key={list.id} value={list.id}>
                    {list.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
              style={{ flex: 1 }}
            >
              {loading ? 'Saving...' : (contact ? 'Update' : 'Create')}
            </button>
            <button
              type="button"
              className="btn"
              onClick={onClose}
              disabled={loading}
              style={{ 
                flex: 1,
                backgroundColor: '#6c757d',
                color: 'white'
              }}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContactForm;
