#!/usr/bin/env python3
"""
Sales Lead Data Enrichment Tool
Uses crawl4ai for web scraping and OpenAI GPT-5 Nano for AI-powered business insights
"""

import pandas as pd
import asyncio
import os
import sys
from datetime import datetime
import json
import logging
from typing import Dict, List, Optional, Tuple
import time

# Third-party imports
from crawl4ai import Async<PERSON>ebCrawler
from openai import OpenAI
import requests
from urllib.parse import urlparse
from linkedin_enrichment import LinkedInEnricher

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enrichment.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LeadEnrichmentTool:
    """Main class for enriching sales lead data with web scraping and AI analysis"""

    def __init__(self, openai_api_key: str, apify_api_key: Optional[str] = None):
        """Initialize the enrichment tool with API keys"""
        self.openai_client = OpenAI(api_key=openai_api_key)
        self.crawler = None
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0

        # Initialize LinkedIn enricher if API key provided
        self.linkedin_enricher = None
        if apify_api_key:
            self.linkedin_enricher = LinkedInEnricher(apify_api_key)

        # New columns to add to CSV (website enrichment)
        self.new_columns = [
            'ai_company_summary',
            'ai_business_insights',
            'ai_key_products_services',
            'ai_target_market',
            'ai_sales_opportunities',
            'ai_competitive_advantages',
            'website_scraped_at',
            'enrichment_status'
        ]

        # LinkedIn columns (added if LinkedIn enrichment is enabled)
        self.linkedin_columns = [
            'linkedin_full_name',
            'linkedin_headline',
            'linkedin_about',
            'linkedin_location',
            'linkedin_current_company',
            'linkedin_current_title',
            'linkedin_follower_count',
            'linkedin_connection_count',
            'linkedin_experience_summary',
            'linkedin_education_summary',
            'linkedin_scraped_at',
            'linkedin_enrichment_status'
        ]
    
    async def initialize_crawler(self):
        """Initialize the async web crawler"""
        try:
            # We'll create the crawler fresh for each use with context manager
            logger.info("Web crawler initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize crawler: {e}")
            raise

    async def cleanup_crawler(self):
        """Clean up the web crawler"""
        # AsyncWebCrawler with context manager handles cleanup automatically
        logger.info("Web crawler cleaned up")
    
    def is_valid_url(self, url: str) -> bool:
        """Check if URL is valid and accessible"""
        if not url or pd.isna(url) or url.strip() == '':
            return False
        
        try:
            parsed = urlparse(url)
            return bool(parsed.netloc and parsed.scheme in ['http', 'https'])
        except:
            return False
    
    async def scrape_website(self, url: str) -> Optional[str]:
        """Scrape website content using crawl4ai"""
        if not self.is_valid_url(url):
            logger.warning(f"Invalid URL: {url}")
            return None

        try:
            logger.info(f"Scraping website: {url}")

            # Use crawl4ai with proper config and context manager
            from crawl4ai import BrowserConfig, CrawlerRunConfig, CacheMode

            browser_config = BrowserConfig(
                headless=True,
                verbose=False,
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            )
            run_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                word_count_threshold=5,  # Lower threshold
                page_timeout=45000,  # Longer timeout
                wait_for_images=False,
                process_iframes=False
            )

            async with AsyncWebCrawler(config=browser_config) as crawler:
                result = await crawler.arun(url=url, config=run_config)

                if hasattr(result, 'success') and result.success:
                    # Extract meaningful content (limit to reasonable size for AI processing)
                    content = ""
                    if hasattr(result, 'markdown') and result.markdown:
                        content = result.markdown[:8000]
                    elif hasattr(result, 'cleaned_html') and result.cleaned_html:
                        content = result.cleaned_html[:8000]
                    elif hasattr(result, 'html') and result.html:
                        content = result.html[:8000]

                    logger.info(f"Successfully scraped {len(content)} characters from {url}")

                    # Check if we have meaningful content
                    if content and content.strip() and len(content.strip()) > 10:
                        return content.strip()
                    else:
                        logger.warning(f"Scraped content too short or empty for {url} (got {len(content.strip()) if content else 0} characters)")
                        # For debugging: let's try to use even short content if it exists
                        if content and content.strip():
                            logger.info(f"Using short content anyway: '{content.strip()[:100]}...'")
                            return content.strip()
                        return None
                else:
                    error_msg = getattr(result, 'error_message', 'Unknown error')
                    logger.warning(f"Failed to scrape {url}: {error_msg}")
                    return None

        except Exception as e:
            logger.error(f"Error scraping {url}: {e}")
            return None

    def analyze_with_ai_fallback(self, company_name: str, industry: str, website_url: str) -> Optional[Dict[str, str]]:
        """Fallback AI analysis when website scraping fails"""
        try:
            prompt = f"""
Based on the company name "{company_name}" and industry "{industry}" and website URL "{website_url}",
provide business insights for this company. Use your knowledge to make educated assumptions.

Please provide:
1. Company Summary (2-3 sentences about what this company likely does)
2. Business Insights (key business characteristics, market position)
3. Key Products/Services (main offerings based on company name and industry)
4. Target Market (likely customer segments)
5. Sales Opportunities (potential sales angles for coffee wholesale)
6. Competitive Advantages (likely strengths based on company name/industry)

Format as JSON with keys: company_summary, business_insights, key_products_services, target_market, sales_opportunities, competitive_advantages
"""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are a business analyst providing insights about companies for B2B sales purposes. Provide realistic, helpful analysis based on available information."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.3
            )

            if response.choices and response.choices[0].message.content:
                content = response.choices[0].message.content.strip()

                # Try to extract JSON from the response
                try:
                    # Remove any markdown formatting
                    if content.startswith('```json'):
                        content = content[7:]
                    if content.endswith('```'):
                        content = content[:-3]

                    import json
                    return json.loads(content)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from fallback analysis for {company_name}")
                    return None

            return None

        except Exception as e:
            logger.error(f"Error in fallback AI analysis for {company_name}: {e}")
            return None
    
    def analyze_with_ai(self, website_content: str, company_name: str, industry: str = "") -> Dict[str, str]:
        """Use OpenAI GPT-5 Nano to analyze website content and generate business insights"""
        try:
            prompt = f"""
            Analyze the following website content for {company_name} (Industry: {industry}) and provide detailed business insights for sales purposes.
            
            Website Content:
            {website_content}
            
            Please provide a comprehensive analysis in the following JSON format:
            {{
                "company_summary": "Brief 2-3 sentence summary of what the company does",
                "business_insights": "Key business insights, pain points, and market position",
                "key_products_services": "Main products/services offered",
                "target_market": "Who their customers are and market segments they serve",
                "sales_opportunities": "Potential sales angles and opportunities for our products/services",
                "competitive_advantages": "What makes them unique or competitive in their market"
            }}
            
            Focus on actionable insights that would help a salesperson understand this company and craft a targeted pitch.
            """
            
            logger.info(f"Analyzing content for {company_name} with GPT-5 Nano")
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",  # Using GPT-4o-mini as a fallback since GPT-5 Nano may not be available yet
                messages=[
                    {"role": "system", "content": "You are a business analyst expert at extracting sales-relevant insights from company websites. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.3
            )
            
            # Parse the JSON response
            ai_response = response.choices[0].message.content
            if ai_response:
                ai_response = ai_response.strip()
            else:
                ai_response = ""
            
            # Try to extract JSON from the response
            try:
                # Remove any markdown formatting
                if ai_response.startswith('```json'):
                    ai_response = ai_response[7:]
                if ai_response.endswith('```'):
                    ai_response = ai_response[:-3]
                
                insights = json.loads(ai_response)
                logger.info(f"Successfully analyzed {company_name}")
                return insights
                
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse JSON response for {company_name}, using raw response")
                # Fallback: create a basic structure with the raw response
                return {
                    "company_summary": ai_response[:200] + "..." if len(ai_response) > 200 else ai_response,
                    "business_insights": "Analysis available in company summary",
                    "key_products_services": "See company summary",
                    "target_market": "See company summary", 
                    "sales_opportunities": "See company summary",
                    "competitive_advantages": "See company summary"
                }
                
        except Exception as e:
            logger.error(f"Error analyzing content for {company_name}: {e}")
            return {
                "company_summary": f"Error analyzing content: {str(e)}",
                "business_insights": "Analysis failed",
                "key_products_services": "Analysis failed",
                "target_market": "Analysis failed",
                "sales_opportunities": "Analysis failed", 
                "competitive_advantages": "Analysis failed"
            }
    
    async def enrich_single_lead(self, row: pd.Series) -> Dict[str, str]:
        """Enrich a single lead with website data and AI analysis"""
        company_name = row.get('company', 'Unknown Company')
        website_url = row.get('website_url', '')
        linkedin_url = row.get('linkedin_url', '')
        industry = row.get('industry', '')

        logger.info(f"Processing lead: {company_name}")

        # Initialize result with empty values for website enrichment
        result = {col: '' for col in self.new_columns}
        result['enrichment_status'] = 'failed'
        result['website_scraped_at'] = datetime.now().isoformat()

        # Add LinkedIn columns if LinkedIn enricher is available
        if self.linkedin_enricher:
            for col in self.linkedin_columns:
                result[col] = ''

        # Website enrichment
        if self.is_valid_url(website_url):
            # Scrape website content
            website_content = await self.scrape_website(website_url)
            if website_content:
                # Analyze with AI
                ai_insights = self.analyze_with_ai(website_content, company_name, industry)

                # Map AI insights to result columns
                result['ai_company_summary'] = ai_insights.get('company_summary', '')
                result['ai_business_insights'] = ai_insights.get('business_insights', '')
                result['ai_key_products_services'] = ai_insights.get('key_products_services', '')
                result['ai_target_market'] = ai_insights.get('target_market', '')
                result['ai_sales_opportunities'] = ai_insights.get('sales_opportunities', '')
                result['ai_competitive_advantages'] = ai_insights.get('competitive_advantages', '')
                result['enrichment_status'] = 'success'

                logger.info(f"Successfully enriched website data for {company_name}")
            else:
                # Fallback: try to generate insights based on company name and industry
                logger.info(f"Website scraping failed for {company_name}, trying fallback analysis")
                fallback_insights = self.analyze_with_ai_fallback(company_name, industry, website_url)

                if fallback_insights:
                    result['ai_company_summary'] = fallback_insights.get('company_summary', '')
                    result['ai_business_insights'] = fallback_insights.get('business_insights', '')
                    result['ai_key_products_services'] = fallback_insights.get('key_products_services', '')
                    result['ai_target_market'] = fallback_insights.get('target_market', '')
                    result['ai_sales_opportunities'] = fallback_insights.get('sales_opportunities', '')
                    result['ai_competitive_advantages'] = fallback_insights.get('competitive_advantages', '')
                    result['enrichment_status'] = 'fallback_success'
                    logger.info(f"Successfully enriched with fallback analysis for {company_name}")
                else:
                    result['enrichment_status'] = 'scraping_failed'
                    logger.warning(f"Failed to scrape website and fallback analysis for {company_name}")
        else:
            result['enrichment_status'] = 'no_valid_url'
            logger.warning(f"No valid website URL for {company_name}")

        # LinkedIn enrichment (if enabled and LinkedIn URL available)
        if self.linkedin_enricher and linkedin_url:
            try:
                logger.info(f"Enriching LinkedIn data for {company_name}")
                linkedin_data = self.linkedin_enricher.enrich_with_linkedin(linkedin_url)

                # Add LinkedIn data to result
                for col in self.linkedin_columns:
                    result[col] = linkedin_data.get(col, '')

                if linkedin_data.get('linkedin_enrichment_status') == 'success':
                    logger.info(f"Successfully enriched LinkedIn data for {company_name}")
                else:
                    logger.warning(f"LinkedIn enrichment failed for {company_name}: {linkedin_data.get('linkedin_enrichment_status')}")

            except Exception as e:
                logger.error(f"Error during LinkedIn enrichment for {company_name}: {e}")
                result['linkedin_enrichment_status'] = 'error'

        # Update success count if any enrichment succeeded
        if (result.get('enrichment_status') == 'success' or
            result.get('linkedin_enrichment_status') == 'success'):
            self.success_count += 1

        return result
    
    async def process_csv_file(self, csv_file_path: str, max_leads: Optional[int] = None, start_from: int = 0):
        """Process a CSV file and enrich leads with website data and AI analysis"""
        logger.info(f"Starting to process {csv_file_path}")
        
        # Read CSV file
        try:
            df = pd.read_csv(csv_file_path)
            logger.info(f"Loaded {len(df)} leads from {csv_file_path}")
        except Exception as e:
            logger.error(f"Failed to read CSV file {csv_file_path}: {e}")
            return
        
        # Add new columns if they don't exist
        all_columns = self.new_columns.copy()
        if self.linkedin_enricher:
            all_columns.extend(self.linkedin_columns)

        for col in all_columns:
            if col not in df.columns:
                df[col] = ''
        
        # Determine which leads to process
        if max_leads:
            end_idx = min(start_from + max_leads, len(df))
        else:
            end_idx = len(df)
        
        leads_to_process = df.iloc[start_from:end_idx]
        logger.info(f"Processing leads {start_from} to {end_idx-1} ({len(leads_to_process)} leads)")
        
        # Process each lead
        for idx, row in leads_to_process.iterrows():
            try:
                self.processed_count += 1
                
                # Skip if already enriched (has ai_company_summary)
                if pd.notna(row.get('ai_company_summary', '')) and row.get('ai_company_summary', '').strip():
                    logger.info(f"Skipping already enriched lead: {row.get('company', 'Unknown')}")
                    continue
                
                # Enrich the lead
                enrichment_data = await self.enrich_single_lead(row)
                
                # Update the dataframe
                for col, value in enrichment_data.items():
                    df.at[idx, col] = value
                
                # Save progress every 10 leads
                if self.processed_count % 10 == 0:
                    self.save_progress(df, csv_file_path)
                    logger.info(f"Progress saved. Processed: {self.processed_count}, Success: {self.success_count}")
                
                # Add delay to be respectful to websites
                await asyncio.sleep(2)
                
            except Exception as e:
                self.error_count += 1
                logger.error(f"Error processing lead {idx}: {e}")
                continue
        
        # Final save
        self.save_progress(df, csv_file_path)
        logger.info(f"Completed processing {csv_file_path}. Total processed: {self.processed_count}, Success: {self.success_count}, Errors: {self.error_count}")
    
    def save_progress(self, df: pd.DataFrame, original_file_path: str):
        """Save progress to a new file"""
        try:
            # Create backup filename
            base_name = os.path.splitext(original_file_path)[0]
            backup_file = f"{base_name}_enriched.csv"
            
            df.to_csv(backup_file, index=False)
            logger.info(f"Progress saved to {backup_file}")
            
        except Exception as e:
            logger.error(f"Failed to save progress: {e}")

# Main execution functions
async def main():
    """Main function to run the enrichment process"""
    # Configuration
    OPENAI_API_KEY = "********************************************************************************************************************************************************************"
    APIFY_API_KEY = "**********************************************"

    # Initialize the enrichment tool with both API keys
    enricher = LeadEnrichmentTool(OPENAI_API_KEY, APIFY_API_KEY)
    
    try:
        # Initialize crawler
        await enricher.initialize_crawler()
        
        # Get list of CSV files to process
        csv_files = [f for f in os.listdir('.') if f.endswith('.csv') and not f.endswith('_enriched.csv')]
        
        print("Available CSV files:")
        for i, file in enumerate(csv_files):
            print(f"{i+1}. {file}")
        
        # For now, let's start with a test run on the first file
        if csv_files:
            test_file = csv_files[0]
            print(f"\nStarting test enrichment on {test_file} (first 5 leads)")
            await enricher.process_csv_file(test_file, max_leads=5, start_from=0)
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
    finally:
        # Clean up
        await enricher.cleanup_crawler()

if __name__ == "__main__":
    asyncio.run(main())
