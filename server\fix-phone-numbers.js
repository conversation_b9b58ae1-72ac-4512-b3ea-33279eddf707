#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');

// File paths
const DATA_DIR = '../data';
const CONTACTS_FILE = path.join(DATA_DIR, 'contacts.json');
const BACKUP_FILE = path.join(DATA_DIR, `contacts_phone_backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`);

// Helper function to check if a 3-digit code is a valid US area code
function isValidUSAreaCode(areaCode) {
  // Common US area codes - this is a simplified check
  // In reality, you'd want a more comprehensive list
  const validAreaCodes = [
    '201', '202', '203', '205', '206', '207', '208', '209', '210', '212', '213', '214', '215', '216', '217', '218', '219',
    '224', '225', '228', '229', '231', '234', '239', '240', '248', '251', '252', '253', '254', '256', '260', '262',
    '267', '269', '270', '276', '281', '301', '302', '303', '304', '305', '307', '308', '309', '310', '312', '313',
    '314', '315', '316', '317', '318', '319', '320', '321', '323', '325', '330', '331', '334', '336', '337', '339',
    '347', '351', '352', '360', '361', '386', '401', '402', '404', '405', '406', '407', '408', '409', '410', '412',
    '413', '414', '415', '417', '419', '423', '424', '425', '430', '432', '434', '435', '440', '443', '458', '469',
    '470', '475', '478', '479', '480', '484', '501', '502', '503', '504', '505', '507', '508', '509', '510', '512',
    '513', '515', '516', '517', '518', '520', '530', '540', '541', '551', '559', '561', '562', '563', '567', '570',
    '571', '573', '574', '575', '580', '585', '586', '601', '602', '603', '605', '606', '607', '608', '609', '610',
    '612', '614', '615', '616', '617', '618', '619', '620', '623', '626', '630', '631', '636', '641', '646', '650',
    '651', '660', '661', '662', '667', '678', '682', '701', '702', '703', '704', '706', '707', '708', '712', '713',
    '714', '715', '716', '717', '718', '719', '720', '724', '727', '731', '732', '734', '737', '740', '757', '760',
    '763', '765', '770', '772', '773', '774', '775', '781', '785', '786', '801', '802', '803', '804', '805', '806',
    '808', '810', '812', '813', '814', '815', '816', '817', '818', '828', '830', '831', '832', '843', '845', '847',
    '848', '850', '856', '857', '858', '859', '860', '862', '863', '864', '865', '870', '872', '878', '901', '903',
    '904', '906', '907', '908', '909', '910', '912', '913', '914', '915', '916', '917', '918', '919', '920', '925',
    '928', '929', '931', '936', '937', '940', '941', '947', '949', '951', '952', '954', '956', '970', '971', '972',
    '973', '978', '979', '980', '985', '989'
  ];
  return validAreaCodes.includes(areaCode);
}

// Helper function to clean and format phone numbers
function formatPhoneNumber(phoneStr) {
  if (!phoneStr || phoneStr.trim() === '') {
    return '';
  }

  // Convert to string and clean
  let phone = String(phoneStr).trim();

  // Handle decimal format FIRST (remove .0 at the end)
  if (phone.endsWith('.0')) {
    phone = phone.slice(0, -2);
  }

  // Remove common formatting characters
  phone = phone.replace(/[\s\-\(\)\+\.]/g, '');

  // Remove any non-digit characters except for leading country codes
  phone = phone.replace(/[^\d]/g, '');
  
  // Skip if no digits found
  if (!phone || phone.length === 0) {
    return '';
  }
  
  // Handle different phone number lengths
  if (phone.length === 10) {
    // US/Canada number without country code: 1234567890 -> +****************
    return `+1 (${phone.slice(0, 3)}) ${phone.slice(3, 6)}-${phone.slice(6)}`;
  } else if (phone.length === 11) {
    // Check if it starts with 1 (US/Canada country code)
    if (phone.startsWith('1')) {
      // US/Canada number with country code: 11234567890 -> +****************
      const areaCode = phone.slice(1, 4);
      const exchange = phone.slice(4, 7);
      const number = phone.slice(7);
      return `+1 (${areaCode}) ${exchange}-${number}`;
    } else {
      // 11 digits not starting with 1 - check if it could be a US number
      // First, check if digits 1-3 are a valid US area code (missing leading 1)
      const possibleAreaCode = phone.slice(1, 4);
      if (isValidUSAreaCode(possibleAreaCode)) {
        // This looks like: 16143000648 -> should be **************
        return `+1 (${phone.slice(1, 4)}) ${phone.slice(4, 7)}-${phone.slice(7)}`;
      }
      // Check if first 3 digits are a valid area code (11-digit number starting with area code)
      const firstThreeDigits = phone.slice(0, 3);
      if (isValidUSAreaCode(firstThreeDigits) && phone.length === 11) {
        // This would be unusual but possible: 61430006480 -> +**************** (but this is 11 digits, so probably not right)
        // More likely it's international
        return `+${phone.slice(0, 2)} ${phone.slice(2, 5)} ${phone.slice(5, 8)}-${phone.slice(8)}`;
      }
      // Otherwise treat as international: format as +XX XXXXXXXXX
      return `+${phone.slice(0, 2)} ${phone.slice(2, 5)} ${phone.slice(5, 8)}-${phone.slice(8)}`;
    }
  } else if (phone.length === 12) {
    // International number: format as +XXX XXXXXXXXX
    return `+${phone.slice(0, 3)} ${phone.slice(3, 6)} ${phone.slice(6, 9)}-${phone.slice(9)}`;
  } else if (phone.length === 13) {
    // International number: format as +XXXX XXXXXXXXX
    return `+${phone.slice(0, 4)} ${phone.slice(4, 7)} ${phone.slice(7, 10)}-${phone.slice(10)}`;
  } else if (phone.length >= 7 && phone.length <= 9) {
    // Local number, assume US and add country code
    if (phone.length === 7) {
      // 7 digits: XXX-XXXX (missing area code, can't format properly)
      return `${phone.slice(0, 3)}-${phone.slice(3)}`;
    } else if (phone.length === 8) {
      // 8 digits: XXXX-XXXX
      return `${phone.slice(0, 4)}-${phone.slice(4)}`;
    } else if (phone.length === 9) {
      // 9 digits: XXX-XXX-XXX
      return `${phone.slice(0, 3)}-${phone.slice(3, 6)}-${phone.slice(6)}`;
    }
  } else if (phone.length > 13) {
    // Very long number, try to format as international
    return `+${phone.slice(0, 2)} ${phone.slice(2)}`;
  }
  
  // If we can't determine the format, return the cleaned digits
  return phone;
}

// Helper function to detect country from phone number
function detectCountry(phone) {
  if (!phone) return 'Unknown';
  
  const cleaned = phone.replace(/[^\d]/g, '');
  
  if (cleaned.startsWith('1') && cleaned.length === 11) {
    return 'US/Canada';
  } else if (cleaned.startsWith('31') && cleaned.length >= 11) {
    return 'Netherlands';
  } else if (cleaned.startsWith('27') && cleaned.length >= 11) {
    return 'South Africa';
  } else if (cleaned.startsWith('44') && cleaned.length >= 11) {
    return 'United Kingdom';
  } else if (cleaned.length === 10) {
    return 'US/Canada (assumed)';
  }
  
  return 'International';
}

async function main() {
  try {
    console.log('📞 Starting phone number cleanup...');
    
    // Step 1: Backup current contacts
    console.log('📦 Creating backup...');
    const currentContacts = JSON.parse(await fs.readFile(CONTACTS_FILE, 'utf8'));
    await fs.writeFile(BACKUP_FILE, JSON.stringify(currentContacts, null, 2));
    console.log(`✅ Backup created: ${path.basename(BACKUP_FILE)}`);
    
    // Step 2: Analyze current phone number formats
    console.log('🔍 Analyzing current phone number formats...');
    const phoneStats = {
      total: 0,
      withPhone: 0,
      decimal: 0,
      formatted: 0,
      international: 0,
      countries: {}
    };
    
    const problemNumbers = [];
    
    for (const contact of currentContacts) {
      phoneStats.total++;
      
      if (contact.phone && contact.phone.trim() !== '') {
        phoneStats.withPhone++;
        
        const originalPhone = contact.phone;
        const country = detectCountry(originalPhone);
        phoneStats.countries[country] = (phoneStats.countries[country] || 0) + 1;
        
        if (originalPhone.includes('.0')) {
          phoneStats.decimal++;
          problemNumbers.push({
            email: contact.email,
            original: originalPhone,
            type: 'decimal'
          });
        } else if (originalPhone.startsWith('+')) {
          phoneStats.formatted++;
        } else if (!originalPhone.startsWith('+') && originalPhone.includes('-')) {
          phoneStats.international++;
        }
      }
    }
    
    console.log('📊 Current phone number analysis:');
    console.log(`   • Total contacts: ${phoneStats.total}`);
    console.log(`   • Contacts with phone numbers: ${phoneStats.withPhone}`);
    console.log(`   • Decimal format numbers: ${phoneStats.decimal}`);
    console.log(`   • Already formatted (+X): ${phoneStats.formatted}`);
    console.log(`   • Other formats: ${phoneStats.international}`);
    console.log('   • Countries detected:');
    Object.entries(phoneStats.countries).forEach(([country, count]) => {
      console.log(`     - ${country}: ${count}`);
    });
    
    // Step 3: Clean and format phone numbers
    console.log('\n🔧 Cleaning and formatting phone numbers...');
    let updatedCount = 0;
    let errorCount = 0;
    const formatExamples = {};
    
    for (const contact of currentContacts) {
      if (contact.phone && contact.phone.trim() !== '') {
        const originalPhone = contact.phone;
        const formattedPhone = formatPhoneNumber(originalPhone);
        
        if (formattedPhone !== originalPhone) {
          // Store example for each format type
          const country = detectCountry(originalPhone);
          if (!formatExamples[country]) {
            formatExamples[country] = {
              original: originalPhone,
              formatted: formattedPhone
            };
          }
          
          contact.phone = formattedPhone;
          contact.updatedAt = new Date().toISOString();
          updatedCount++;
        }
        
        if (!formattedPhone || formattedPhone === originalPhone.replace(/[^\d]/g, '')) {
          errorCount++;
        }
      }
    }
    
    // Step 4: Save updated contacts
    console.log('💾 Saving updated contacts...');
    await fs.writeFile(CONTACTS_FILE, JSON.stringify(currentContacts, null, 2));
    
    // Step 5: Report results
    console.log('\n🎉 Phone number cleanup completed!');
    console.log(`📊 Summary:`);
    console.log(`   • Total contacts processed: ${currentContacts.length}`);
    console.log(`   • Phone numbers updated: ${updatedCount}`);
    console.log(`   • Numbers that couldn't be formatted: ${errorCount}`);
    console.log(`   • Backup saved to: ${path.basename(BACKUP_FILE)}`);
    
    if (Object.keys(formatExamples).length > 0) {
      console.log('\n📝 Format examples:');
      Object.entries(formatExamples).forEach(([country, example]) => {
        console.log(`   • ${country}:`);
        console.log(`     Before: "${example.original}"`);
        console.log(`     After:  "${example.formatted}"`);
      });
    }
    
    // Step 6: Final verification
    console.log('\n🔍 Final verification...');
    const finalStats = {
      withPhone: 0,
      formatted: 0,
      decimal: 0
    };
    
    for (const contact of currentContacts) {
      if (contact.phone && contact.phone.trim() !== '') {
        finalStats.withPhone++;
        if (contact.phone.startsWith('+')) {
          finalStats.formatted++;
        }
        if (contact.phone.includes('.0')) {
          finalStats.decimal++;
        }
      }
    }
    
    console.log(`   • Contacts with phone numbers: ${finalStats.withPhone}`);
    console.log(`   • Properly formatted (+X): ${finalStats.formatted}`);
    console.log(`   • Still in decimal format: ${finalStats.decimal}`);
    
    if (finalStats.decimal === 0) {
      console.log('✅ All decimal format numbers have been cleaned!');
    }
    
  } catch (error) {
    console.error('❌ Error during phone cleanup:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, formatPhoneNumber };
