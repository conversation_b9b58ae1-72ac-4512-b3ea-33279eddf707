#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');
const csv = require('csv-parser');
const { createReadStream } = require('fs');

// File paths
const DATA_DIR = '../data';
const ORIGINAL_DATA_DIR = '../original_data';
const CONTACTS_FILE = path.join(DATA_DIR, 'contacts.json');
const BACKUP_FILE = path.join(DATA_DIR, `contacts_backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`);

// Helper function to read CSV file
function readCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

// Helper function to clean and normalize data
function cleanValue(value) {
  if (!value || value === 'null' || value === 'undefined' || value === 'nan') {
    return '';
  }
  return String(value).trim();
}

// Helper function to extract social media data from CSV row
function extractSocialMediaData(row) {
  const data = {};

  // Basic social media fields - ensure they are valid URLs
  data.linkedinUrl = cleanValue(row.linkedin_url || '');
  data.facebookUrl = cleanValue(row.facebook_url || '');
  data.instagramUrl = cleanValue(row.instagram_url || '');
  data.twitterUrl = cleanValue(row.twitter_url || '');

  // Validate URLs - only keep if they look like actual URLs
  if (data.linkedinUrl && !data.linkedinUrl.includes('linkedin.com')) data.linkedinUrl = '';
  if (data.facebookUrl && !data.facebookUrl.includes('facebook.com')) data.facebookUrl = '';
  if (data.instagramUrl && !data.instagramUrl.includes('instagram.com')) data.instagramUrl = '';
  if (data.twitterUrl && !data.twitterUrl.includes('twitter.com')) data.twitterUrl = '';
  
  // Enhanced LinkedIn data
  data.linkedinFullName = cleanValue(row.linkedin_full_name || '');
  data.linkedinHeadline = cleanValue(row.linkedin_headline || '');
  data.linkedinAbout = cleanValue(row.linkedin_about || '');
  data.linkedinLocation = cleanValue(row.linkedin_location || '');
  data.linkedinCurrentCompany = cleanValue(row.linkedin_current_company || '');
  data.linkedinCurrentTitle = cleanValue(row.linkedin_current_title || '');
  data.linkedinFollowerCount = cleanValue(row.linkedin_follower_count || '');
  data.linkedinConnectionCount = cleanValue(row.linkedin_connection_count || '');
  data.linkedinExperienceSummary = cleanValue(row.linkedin_experience_summary || '');
  data.linkedinEducationSummary = cleanValue(row.linkedin_education_summary || '');
  
  // AI enrichment data
  data.aiCompanySummary = cleanValue(row.ai_company_summary || '');
  data.aiBusinessInsights = cleanValue(row.ai_business_insights || '');
  data.aiKeyProductsServices = cleanValue(row.ai_key_products_services || '');
  data.aiTargetMarket = cleanValue(row.ai_target_market || '');
  data.aiSalesOpportunities = cleanValue(row.ai_sales_opportunities || '');
  data.aiCompetitiveAdvantages = cleanValue(row.ai_competitive_advantages || '');
  
  return data;
}

// Helper function to extract name data from CSV row
function extractNameData(row) {
  return {
    firstName: cleanValue(row.first_name || row.firstName || ''),
    lastName: cleanValue(row.last_name || row.lastName || ''),
    fullName: cleanValue(row.name || row.full_name || '')
  };
}

async function main() {
  try {
    console.log('🔧 Starting contact data repair...');
    
    // Step 1: Backup current contacts
    console.log('📦 Creating backup...');
    const currentContacts = JSON.parse(await fs.readFile(CONTACTS_FILE, 'utf8'));
    await fs.writeFile(BACKUP_FILE, JSON.stringify(currentContacts, null, 2));
    console.log(`✅ Backup created: ${BACKUP_FILE}`);
    
    // Step 2: Read all CSV files from original_data
    console.log('📂 Reading original CSV files...');
    const csvFiles = await fs.readdir(ORIGINAL_DATA_DIR);
    const csvFilePaths = csvFiles
      .filter(file => file.endsWith('.csv'))
      .map(file => path.join(ORIGINAL_DATA_DIR, file));
    
    console.log(`Found ${csvFilePaths.length} CSV files:`, csvFiles.filter(f => f.endsWith('.csv')));
    
    // Step 3: Build email-to-data mapping from all CSV files
    console.log('🗺️  Building email-to-data mapping...');
    const emailToDataMap = new Map();
    
    for (const csvFile of csvFilePaths) {
      console.log(`  Processing: ${path.basename(csvFile)}`);
      try {
        const csvData = await readCSV(csvFile);
        
        for (const row of csvData) {
          const email = cleanValue(row.email);
          if (!email) continue;
          
          const socialMediaData = extractSocialMediaData(row);
          const nameData = extractNameData(row);
          
          // Store the data, preferring data from files with more complete information
          if (!emailToDataMap.has(email) || hasMoreCompleteData(socialMediaData, emailToDataMap.get(email)?.socialMedia)) {
            emailToDataMap.set(email, {
              socialMedia: socialMediaData,
              name: nameData,
              sourceFile: path.basename(csvFile)
            });
          }
        }
      } catch (error) {
        console.warn(`  ⚠️  Warning: Could not process ${path.basename(csvFile)}: ${error.message}`);
      }
    }
    
    console.log(`📊 Built mapping for ${emailToDataMap.size} unique email addresses`);
    
    // Step 4: Update contacts with missing data
    console.log('🔄 Updating contacts...');
    let updatedCount = 0;
    let socialMediaUpdates = 0;
    let nameUpdates = 0;
    
    for (const contact of currentContacts) {
      const email = contact.email?.toLowerCase();
      if (!email || !emailToDataMap.has(email)) continue;
      
      const originalData = emailToDataMap.get(email);
      let contactUpdated = false;
      
      // Update social media fields if they're empty or invalid
      const socialFields = ['linkedinUrl', 'facebookUrl', 'instagramUrl', 'twitterUrl'];
      for (const field of socialFields) {
        const currentValue = contact[field] || '';
        const newValue = originalData.socialMedia[field] || '';

        // Update if current is empty, or if current is invalid (like "success") and we have a valid URL
        const shouldUpdate = (!currentValue ||
                            currentValue === 'success' ||
                            !currentValue.startsWith('http')) &&
                           newValue &&
                           newValue.startsWith('http');

        if (shouldUpdate) {
          contact[field] = newValue;
          contactUpdated = true;
          socialMediaUpdates++;
        }
      }
      
      // Update lastName if it's empty but we have it in original data
      if ((!contact.lastName || contact.lastName === '') && originalData.name.lastName) {
        contact.lastName = originalData.name.lastName;
        contactUpdated = true;
        nameUpdates++;
      }
      
      // Update name if it's incomplete
      if (originalData.name.fullName && (!contact.name || contact.name.trim() === contact.firstName)) {
        contact.name = originalData.name.fullName;
        contactUpdated = true;
      }
      
      // Add enhanced LinkedIn data to customFields if available
      if (originalData.socialMedia.linkedinFullName) {
        if (!contact.customFields) contact.customFields = {};
        
        const linkedinFields = {
          'Linkedin Full Name': 'linkedinFullName',
          'Linkedin Headline': 'linkedinHeadline', 
          'Linkedin About': 'linkedinAbout',
          'Linkedin Location': 'linkedinLocation',
          'Linkedin Current Company': 'linkedinCurrentCompany',
          'Linkedin Current Title': 'linkedinCurrentTitle',
          'Linkedin Follower Count': 'linkedinFollowerCount',
          'Linkedin Connection Count': 'linkedinConnectionCount',
          'Linkedin Experience Summary': 'linkedinExperienceSummary',
          'Linkedin Education Summary': 'linkedinEducationSummary'
        };
        
        for (const [customFieldName, dataKey] of Object.entries(linkedinFields)) {
          if (originalData.socialMedia[dataKey] && !contact.customFields[customFieldName]) {
            contact.customFields[customFieldName] = originalData.socialMedia[dataKey];
            contactUpdated = true;
          }
        }
      }
      
      // Add AI enrichment data to customFields if available
      if (originalData.socialMedia.aiCompanySummary) {
        if (!contact.customFields) contact.customFields = {};
        
        const aiFields = {
          'AI Company Summary': 'aiCompanySummary',
          'AI Business Insights': 'aiBusinessInsights',
          'AI Key Products Services': 'aiKeyProductsServices',
          'AI Target Market': 'aiTargetMarket',
          'AI Sales Opportunities': 'aiSalesOpportunities',
          'AI Competitive Advantages': 'aiCompetitiveAdvantages'
        };
        
        for (const [customFieldName, dataKey] of Object.entries(aiFields)) {
          if (originalData.socialMedia[dataKey] && !contact.customFields[customFieldName]) {
            contact.customFields[customFieldName] = originalData.socialMedia[dataKey];
            contactUpdated = true;
          }
        }
      }
      
      if (contactUpdated) {
        contact.updatedAt = new Date().toISOString();
        updatedCount++;
      }
    }
    
    // Step 5: Save updated contacts
    console.log('💾 Saving updated contacts...');
    await fs.writeFile(CONTACTS_FILE, JSON.stringify(currentContacts, null, 2));
    
    // Step 6: Report results
    console.log('\n🎉 Contact data repair completed!');
    console.log(`📊 Summary:`);
    console.log(`   • Total contacts processed: ${currentContacts.length}`);
    console.log(`   • Contacts updated: ${updatedCount}`);
    console.log(`   • Social media fields updated: ${socialMediaUpdates}`);
    console.log(`   • Name fields updated: ${nameUpdates}`);
    console.log(`   • Original data sources: ${csvFilePaths.length} CSV files`);
    console.log(`   • Backup saved to: ${path.basename(BACKUP_FILE)}`);
    
  } catch (error) {
    console.error('❌ Error during repair:', error);
    process.exit(1);
  }
}

// Helper function to determine if one social media data set is more complete than another
function hasMoreCompleteData(newData, existingData) {
  if (!existingData) return true;
  
  const countNonEmpty = (data) => {
    if (!data) return 0;
    return Object.values(data).filter(value => value && value !== '').length;
  };
  
  return countNonEmpty(newData) > countNonEmpty(existingData);
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
