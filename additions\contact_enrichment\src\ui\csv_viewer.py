#!/usr/bin/env python3
"""
Simple CSV Viewer with UI for exploring lead data
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import os
import asyncio
import threading
from pathlib import Path
from dotenv import load_dotenv
from lead_enrichment import LeadEnrichmentTool
from sales_email_generator import SalesEmailGenerator

# Load environment variables
load_dotenv()

class CSVViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("Lead Data CSV Viewer & Enrichment Dashboard")
        self.root.geometry("1400x900")

        self.current_df = None
        self.current_file = None
        self.selected_row_index = None

        # Initialize enrichment tools with environment variables
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.apify_api_key = os.getenv('APIFY_API_KEY')
        self.enrichment_tool = None
        self.email_generator = None

        # Check for required API keys
        if not self.openai_api_key:
            messagebox.showerror("Missing API Key",
                               "OpenAI API key not found in .env file.\n"
                               "Please add OPENAI_API_KEY to your .env file.")
            return

        self.setup_ui()
        self.load_csv_files()
        self.update_total_stats()
    
    def setup_ui(self):
        """Setup the user interface"""
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky="nsew")
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)  # Data view gets most space
        main_frame.rowconfigure(4, weight=0)  # Stats frame doesn't expand
        
        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="Select CSV File", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)

        ttk.Label(file_frame, text="File:").grid(row=0, column=0, sticky="w", padx=(0, 5))

        self.file_var = tk.StringVar()
        self.file_combo = ttk.Combobox(file_frame, textvariable=self.file_var, state="readonly")
        self.file_combo.grid(row=0, column=1, sticky="ew", padx=(0, 5))
        self.file_combo.bind('<<ComboboxSelected>>', self.on_file_selected)

        ttk.Button(file_frame, text="Refresh", command=self.load_csv_files).grid(row=0, column=2)

        # Action buttons frame
        action_frame = ttk.LabelFrame(main_frame, text="Actions", padding="5")
        action_frame.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))

        # Selected row info
        self.selected_info_var = tk.StringVar(value="No row selected")
        ttk.Label(action_frame, textvariable=self.selected_info_var).grid(row=0, column=0, sticky="w", padx=(0, 20))

        # Action buttons
        self.enrich_btn = ttk.Button(action_frame, text="🔍 Enrich Selected Row",
                                   command=self.enrich_selected_row, state="disabled")
        self.enrich_btn.grid(row=0, column=1, padx=(0, 10))

        self.email_btn = ttk.Button(action_frame, text="📧 Generate Sales Email",
                                  command=self.generate_email_for_selected, state="disabled")
        self.email_btn.grid(row=0, column=2, padx=(0, 10))

        self.details_btn = ttk.Button(action_frame, text="👁️ View Details",
                                    command=self.show_lead_details, state="disabled")
        self.details_btn.grid(row=0, column=3, padx=(0, 10))

        # Progress bar for operations
        self.progress_var = tk.StringVar(value="Ready")
        ttk.Label(action_frame, textvariable=self.progress_var).grid(row=0, column=4, sticky="w", padx=(20, 0))

        # Stats frame
        self.stats_frame = ttk.LabelFrame(main_frame, text="Data Summary", padding="5")
        self.stats_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(0, 10))

        # Create a paned window for resizable sections
        self.paned_window = ttk.PanedWindow(main_frame, orient=tk.VERTICAL)
        self.paned_window.grid(row=3, column=0, columnspan=2, sticky="nsew", pady=(0, 10))

        # Data frame (top pane)
        data_frame = ttk.LabelFrame(self.paned_window, text="Data View", padding="5")
        data_frame.columnconfigure(0, weight=1)
        data_frame.rowconfigure(0, weight=1)

        # Treeview for data display
        self.tree = ttk.Treeview(data_frame)
        self.tree.grid(row=0, column=0, sticky="nsew")

        # Bind row selection event
        self.tree.bind('<<TreeviewSelect>>', self.on_row_selected)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(data_frame, orient="vertical", command=self.tree.yview)
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(data_frame, orient="horizontal", command=self.tree.xview)
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # Total database stats frame (bottom pane)
        self.total_stats_frame = ttk.LabelFrame(self.paned_window, text="Total Lead Database Statistics", padding="5")
        self.total_stats_frame.columnconfigure(0, weight=1)
        self.total_stats_frame.columnconfigure(1, weight=1)
        self.total_stats_frame.columnconfigure(2, weight=1)

        # Add both frames to the paned window
        self.paned_window.add(data_frame, weight=3)  # Data view gets more space initially
        self.paned_window.add(self.total_stats_frame, weight=1)  # Stats get less space initially
    
    def load_csv_files(self):
        """Load available CSV files"""
        csv_files = []

        # Check current directory
        for f in os.listdir('.'):
            if f.endswith('.csv'):
                csv_files.append(f)

        # Check data directory
        if os.path.exists('data'):
            for f in os.listdir('data'):
                if f.endswith('.csv'):
                    csv_files.append(os.path.join('data', f))

        self.file_combo['values'] = csv_files

        if csv_files and not self.file_var.get():
            self.file_var.set(csv_files[0])
            self.load_csv_data(csv_files[0])
    
    def on_file_selected(self, event=None):
        """Handle file selection"""
        selected_file = self.file_var.get()
        if selected_file:
            self.load_csv_data(selected_file)
    
    def load_csv_data(self, filename):
        """Load and display CSV data"""
        try:
            self.current_df = pd.read_csv(filename)
            self.current_file = filename
            self.display_data()
            self.update_stats()
            self.update_total_stats()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {filename}:\n{str(e)}")
    
    def display_data(self, df=None):
        """Display data in the treeview"""
        if df is None:
            df = self.current_df
        
        if df is None:
            return
        
        # Clear existing data
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Setup columns
        columns = list(df.columns)
        self.tree['columns'] = columns
        self.tree['show'] = 'headings'
        
        # Configure column headings and widths
        for col in columns:
            self.tree.heading(col, text=col)
            # Set column width based on content
            max_width = max(
                len(str(col)) * 8,  # Header width
                df[col].astype(str).str.len().max() * 8 if not df[col].empty else 50
            )
            self.tree.column(col, width=min(max_width, 200), minwidth=50)
        
        # Insert data (limit to first 1000 rows for performance)
        display_df = df.head(1000)
        for index, row in display_df.iterrows():
            values = [str(val) if pd.notna(val) else '' for val in row]
            self.tree.insert('', 'end', values=values)
        
        if len(df) > 1000:
            # Add a note about truncation
            note_values = ['...'] + ['(Showing first 1000 rows)'] + [''] * (len(columns) - 2)
            self.tree.insert('', 'end', values=note_values)
    
    def update_stats(self):
        """Update statistics display"""
        if self.current_df is None:
            return
        
        # Clear existing stats
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
        
        df = self.current_df
        
        # Basic stats
        stats_text = f"File: {self.current_file}\n"
        stats_text += f"Total Records: {len(df):,}\n"
        stats_text += f"Total Columns: {len(df.columns)}\n"
        
        # Email stats
        if 'email' in df.columns:
            email_count = df['email'].notna().sum()
            stats_text += f"Records with Email: {email_count:,} ({email_count/len(df)*100:.1f}%)\n"
        
        # Company stats
        if 'company' in df.columns:
            unique_companies = df['company'].nunique()
            stats_text += f"Unique Companies: {unique_companies:,}\n"
        
        # Location stats
        if 'state' in df.columns:
            unique_states = df['state'].nunique()
            stats_text += f"States Represented: {unique_states}\n"
        
        # Industry stats
        if 'industry' in df.columns:
            unique_industries = df['industry'].nunique()
            stats_text += f"Industries: {unique_industries}\n"
        
        # Social media stats
        social_cols = ['linkedin_url', 'facebook_url', 'twitter_url', 'instagram_url']
        social_stats = []
        for col in social_cols:
            if col in df.columns:
                count = df[col].notna().sum()
                platform = col.replace('_url', '').replace('_', ' ').title()
                social_stats.append(f"{platform}: {count}")
        
        if social_stats:
            stats_text += f"Social Media Links: {', '.join(social_stats)}\n"
        
        # Display stats
        stats_label = ttk.Label(self.stats_frame, text=stats_text, justify=tk.LEFT)
        stats_label.grid(row=0, column=0, sticky="nw")

        # Top companies/industries
        if 'company' in df.columns:
            top_companies = df['company'].value_counts().head(5)
            if not top_companies.empty:
                companies_text = "Top Companies:\n" + "\n".join([f"• {comp}: {count}" for comp, count in top_companies.items()])
                companies_label = ttk.Label(self.stats_frame, text=companies_text, justify=tk.LEFT)
                companies_label.grid(row=0, column=1, sticky="nw", padx=(20, 0))

        if 'industry' in df.columns:
            top_industries = df['industry'].value_counts().head(5)
            if not top_industries.empty:
                industries_text = "Top Industries:\n" + "\n".join([f"• {ind}: {count}" for ind, count in top_industries.items()])
                industries_label = ttk.Label(self.stats_frame, text=industries_text, justify=tk.LEFT)
                industries_label.grid(row=0, column=2, sticky="nw", padx=(20, 0))
    
    def update_total_stats(self):
        """Update total database statistics"""
        # Clear existing stats
        for widget in self.total_stats_frame.winfo_children():
            widget.destroy()

        # Load all CSV files and combine stats
        csv_files = []

        # Check current directory
        for f in os.listdir('.'):
            if f.endswith('.csv'):
                csv_files.append(f)

        # Check data directory
        if os.path.exists('data'):
            for f in os.listdir('data'):
                if f.endswith('.csv'):
                    csv_files.append(os.path.join('data', f))

        total_records = 0
        total_with_email = 0
        total_with_linkedin = 0
        total_with_facebook = 0
        total_with_twitter = 0
        total_with_instagram = 0
        total_with_website = 0
        total_with_phone = 0
        all_companies = set()
        all_industries = set()
        all_states = set()

        file_stats = []

        for csv_file in csv_files:
            try:
                df = pd.read_csv(csv_file)
                records = len(df)
                total_records += records

                # Email coverage - handle different column names
                email_count = 0
                if 'email' in df.columns:
                    email_count = df['email'].notna().sum()
                total_with_email += email_count

                # Social media coverage - handle different column names
                linkedin_count = 0
                facebook_count = 0
                twitter_count = 0
                instagram_count = 0

                if 'linkedin_url' in df.columns:
                    linkedin_count = df['linkedin_url'].notna().sum()
                if 'facebook_url' in df.columns:
                    facebook_count = df['facebook_url'].notna().sum()
                if 'twitter_url' in df.columns:
                    twitter_count = df['twitter_url'].notna().sum()
                if 'instagram_url' in df.columns:
                    instagram_count = df['instagram_url'].notna().sum()

                total_with_linkedin += linkedin_count
                total_with_facebook += facebook_count
                total_with_twitter += twitter_count
                total_with_instagram += instagram_count

                # Website and phone coverage - handle different column names
                website_count = 0
                phone_count = 0

                if 'website_url' in df.columns:
                    website_count = df['website_url'].notna().sum()
                if 'phone' in df.columns:
                    phone_count = df['phone'].notna().sum()

                total_with_website += website_count
                total_with_phone += phone_count

                # Unique companies, industries, states - handle different column names
                company_col = None
                if 'company' in df.columns:
                    company_col = 'company'
                elif 'organization_name' in df.columns:
                    company_col = 'organization_name'

                if company_col:
                    all_companies.update(df[company_col].dropna().unique())

                industry_col = None
                if 'industry' in df.columns:
                    industry_col = 'industry'
                elif 'business_type' in df.columns:
                    industry_col = 'business_type'

                if industry_col:
                    all_industries.update(df[industry_col].dropna().unique())

                if 'state' in df.columns:
                    all_states.update(df['state'].dropna().unique())

                # File-specific stats with file type identification
                file_type = "Business Contacts"
                if "coffee" in csv_file.lower() or "cs_" in csv_file.lower():
                    file_type = "Coffee Shops"
                elif "ceo" in csv_file.lower():
                    file_type = "CEO Contacts"
                elif "restaurant" in csv_file.lower():
                    file_type = "Restaurant Contacts"
                elif "manager" in csv_file.lower():
                    file_type = "Managers"

                file_stats.append({
                    'file': csv_file,
                    'records': records,
                    'email_coverage': f"{email_count/records*100:.1f}%" if records > 0 else "0%",
                    'type': file_type
                })

            except Exception as e:
                print(f"Error reading {csv_file}: {e}")

        # Create summary text
        summary_text = f"TOTAL LEAD DATABASE\n"
        summary_text += f"Total Records: {total_records:,}\n"
        summary_text += f"Unique Companies: {len(all_companies):,}\n"
        summary_text += f"Industries: {len(all_industries)}\n"
        summary_text += f"States: {len(all_states)}\n\n"
        summary_text += f"DATA COVERAGE\n"
        if total_records > 0:
            summary_text += f"Email: {total_with_email:,} ({total_with_email/total_records*100:.1f}%)\n"
            summary_text += f"Phone: {total_with_phone:,} ({total_with_phone/total_records*100:.1f}%)\n"
            summary_text += f"Website: {total_with_website:,} ({total_with_website/total_records*100:.1f}%)"
        else:
            summary_text += f"Email: {total_with_email:,} (0.0%)\n"
            summary_text += f"Phone: {total_with_phone:,} (0.0%)\n"
            summary_text += f"Website: {total_with_website:,} (0.0%)"

        social_text = f"SOCIAL MEDIA COVERAGE\n"
        if total_records > 0:
            social_text += f"LinkedIn: {total_with_linkedin:,} ({total_with_linkedin/total_records*100:.1f}%)\n"
            social_text += f"Facebook: {total_with_facebook:,} ({total_with_facebook/total_records*100:.1f}%)\n"
            social_text += f"Twitter: {total_with_twitter:,} ({total_with_twitter/total_records*100:.1f}%)\n"
            social_text += f"Instagram: {total_with_instagram:,} ({total_with_instagram/total_records*100:.1f}%)\n\n"
        else:
            social_text += f"LinkedIn: {total_with_linkedin:,} (0.0%)\n"
            social_text += f"Facebook: {total_with_facebook:,} (0.0%)\n"
            social_text += f"Twitter: {total_with_twitter:,} (0.0%)\n"
            social_text += f"Instagram: {total_with_instagram:,} (0.0%)\n\n"
        social_text += f"ENRICHMENT POTENTIAL\n"
        social_text += f"Ready for Web Scraping: {total_with_website:,}\n"
        social_text += f"Ready for Social Research: {total_with_linkedin + total_with_facebook:,}\n"
        social_text += f"Multi-channel Outreach: {min(total_with_email, total_with_linkedin):,}"

        files_text = "FILE BREAKDOWN\n"
        for stat in file_stats:
            file_name = stat['file'].replace('.csv', '')
            files_text += f"• {file_name}\n  {stat['type']}: {stat['records']} records ({stat['email_coverage']} email)\n"

        # Display stats in three columns
        summary_label = ttk.Label(self.total_stats_frame, text=summary_text, justify=tk.LEFT, font=('TkDefaultFont', 9))
        summary_label.grid(row=0, column=0, sticky="nw", padx=(0, 20))

        social_label = ttk.Label(self.total_stats_frame, text=social_text, justify=tk.LEFT, font=('TkDefaultFont', 9))
        social_label.grid(row=0, column=1, sticky="nw", padx=(0, 20))

        files_label = ttk.Label(self.total_stats_frame, text=files_text, justify=tk.LEFT, font=('TkDefaultFont', 9))
        files_label.grid(row=0, column=2, sticky="nw")

    def on_row_selected(self, event):
        """Handle row selection in the treeview"""
        selection = self.tree.selection()
        if selection and self.current_df is not None:
            # Get the selected item
            item = selection[0]
            # Get the row index (treeview children are in order)
            children = self.tree.get_children()
            try:
                row_index = children.index(item)
                if row_index < len(self.current_df):  # Make sure it's not the truncation note
                    self.selected_row_index = row_index
                    row_data = self.current_df.iloc[row_index]

                    # Update selected info
                    company = row_data.get('company', 'Unknown Company')
                    name = f"{row_data.get('first_name', '')} {row_data.get('last_name', '')}".strip()
                    if not name:
                        name = row_data.get('full_name', 'Unknown Contact')

                    self.selected_info_var.set(f"Selected: {name} at {company} (Row {row_index + 1})")

                    # Enable/disable buttons based on data availability
                    self.update_button_states(row_data)
                else:
                    self.selected_row_index = None
                    self.selected_info_var.set("No valid row selected")
                    self.enrich_btn.config(state="disabled")
                    self.email_btn.config(state="disabled")
                    self.details_btn.config(state="disabled")
            except ValueError:
                self.selected_row_index = None
                self.selected_info_var.set("No valid row selected")
                self.enrich_btn.config(state="disabled")
                self.email_btn.config(state="disabled")
                self.details_btn.config(state="disabled")
        else:
            self.selected_row_index = None
            self.selected_info_var.set("No row selected")
            self.enrich_btn.config(state="disabled")
            self.email_btn.config(state="disabled")
            self.details_btn.config(state="disabled")

    def update_button_states(self, row_data):
        """Update button states based on available data"""
        # Check if row has basic data for enrichment
        has_website = pd.notna(row_data.get('website_url', '')) and row_data.get('website_url', '') != ''
        has_linkedin = pd.notna(row_data.get('linkedin_url', '')) and row_data.get('linkedin_url', '') != ''

        # Enable enrich button if there's a website or linkedin URL
        if has_website or has_linkedin:
            self.enrich_btn.config(state="normal")
        else:
            self.enrich_btn.config(state="disabled")

        # Check if row has enriched data for email generation
        has_enriched_data = (pd.notna(row_data.get('ai_company_summary', '')) and
                           row_data.get('ai_company_summary', '') != '')

        # Enable email button if there's enriched data
        if has_enriched_data:
            self.email_btn.config(state="normal")
        else:
            self.email_btn.config(state="disabled")

        # Details button is always enabled when a row is selected
        self.details_btn.config(state="normal")

    def enrich_selected_row(self):
        """Enrich the selected row with website and LinkedIn data"""
        if self.selected_row_index is None or self.current_df is None:
            messagebox.showwarning("No Selection", "Please select a row to enrich.")
            return

        row_data = self.current_df.iloc[self.selected_row_index]

        # Initialize enrichment tool if needed
        if self.enrichment_tool is None:
            try:
                if not self.openai_api_key:
                    messagebox.showerror("Error", "OpenAI API key not found. Please check your .env file.")
                    return

                self.enrichment_tool = LeadEnrichmentTool(self.openai_api_key, self.apify_api_key)

                if self.apify_api_key:
                    print("✅ LinkedIn enrichment enabled (Apify API key found)")
                else:
                    print("⚠️ LinkedIn enrichment disabled (no Apify API key found)")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to initialize enrichment tool: {e}")
                return

        # Update progress
        self.progress_var.set("Enriching...")
        self.root.update()

        try:
            # Run enrichment for single lead
            print(f"DEBUG: Starting enrichment for {row_data.get('company', 'Unknown')}")
            enriched_data = asyncio.run(self.enrichment_tool.enrich_single_lead(row_data))
            print(f"DEBUG: Enrichment result: {enriched_data}")

            if enriched_data:
                print(f"DEBUG: Enriched data keys: {list(enriched_data.keys())}")
                print(f"DEBUG: Current DF columns before update: {list(self.current_df.columns)}")

                # Add new columns if they don't exist
                for key, value in enriched_data.items():
                    if key not in self.current_df.columns:
                        print(f"DEBUG: Adding new column: {key}")
                        self.current_df[key] = ''  # Initialize with empty values

                    # Update the specific row
                    self.current_df.at[self.selected_row_index, key] = value

                print(f"DEBUG: Current DF columns after update: {list(self.current_df.columns)}")

                # Save the updated dataframe
                self.current_df.to_csv(self.current_file, index=False)

                # Force reload the CSV file from disk to ensure we get all new columns
                print(f"DEBUG: Reloading CSV file: {self.current_file}")

                # Store the selected row info before reload
                selected_row_backup = self.selected_row_index

                # Completely reload the CSV data
                try:
                    if not self.current_file:
                        print("DEBUG: No current file to reload")
                        return

                    reloaded_df = pd.read_csv(self.current_file)
                    self.current_df = reloaded_df
                    print(f"DEBUG: Reloaded CSV with {len(self.current_df.columns)} columns: {list(self.current_df.columns)}")

                    # Refresh the display with new data
                    self.display_data()
                    self.update_stats()

                    # Restore selection
                    self.selected_row_index = selected_row_backup
                    if self.selected_row_index is not None and self.selected_row_index < len(self.current_df):
                        # Select the row in the treeview
                        children = self.tree.get_children()
                        if self.selected_row_index < len(children):
                            item = children[self.selected_row_index]
                            self.tree.selection_set(item)
                            self.tree.focus(item)

                            # Update button states with fresh data
                            row_data = self.current_df.iloc[self.selected_row_index]
                            self.update_button_states(row_data)

                            # Update selected info display
                            company = row_data.get('company', 'Unknown Company')
                            name = f"{row_data.get('first_name', '')} {row_data.get('last_name', '')}".strip()
                            if not name:
                                name = row_data.get('full_name', 'Unknown Contact')
                            self.selected_info_var.set(f"Selected: {name} at {company} (Row {self.selected_row_index + 1})")

                except Exception as e:
                    print(f"DEBUG: Error reloading CSV: {e}")
                    # Fallback to simple display refresh
                    self.display_data()

                self.progress_var.set("Enrichment completed!")
                messagebox.showinfo("Success", "Row enriched successfully!")
            else:
                self.progress_var.set("Enrichment failed")
                messagebox.showerror("Error", "Failed to enrich the selected row.")

        except Exception as e:
            self.progress_var.set("Error occurred")
            messagebox.showerror("Error", f"Error during enrichment: {e}")

    def generate_email_for_selected(self):
        """Generate a sales email for the selected enriched row"""
        if self.selected_row_index is None or self.current_df is None:
            messagebox.showwarning("No Selection", "Please select a row to generate email for.")
            return

        row_data = self.current_df.iloc[self.selected_row_index]

        # Check if row has enriched data
        if pd.isna(row_data.get('ai_company_summary', '')) or row_data.get('ai_company_summary', '') == '':
            messagebox.showwarning("No Enriched Data", "Please enrich this row first before generating an email.")
            return

        # Initialize email generator if needed
        if self.email_generator is None:
            try:
                if not self.openai_api_key:
                    messagebox.showerror("Error", "OpenAI API key not found. Please check your .env file.")
                    return

                self.email_generator = SalesEmailGenerator(self.openai_api_key)
            except Exception as e:
                messagebox.showerror("Error", f"Failed to initialize email generator: {e}")
                return

        # Update progress
        self.progress_var.set("Generating email...")
        self.root.update()

        try:
            # Generate email
            email_data = self.email_generator.generate_email_for_lead(row_data)

            self.progress_var.set("Email generated!")

            # Show email in popup
            self.show_email_popup(email_data)

        except Exception as e:
            self.progress_var.set("Error occurred")
            messagebox.showerror("Error", f"Error generating email: {e}")

    def show_email_popup(self, email_data):
        """Show the generated email in a popup window"""
        popup = tk.Toplevel(self.root)
        popup.title("Generated Sales Email")
        popup.geometry("800x600")
        popup.transient(self.root)
        popup.grab_set()

        # Main frame
        main_frame = ttk.Frame(popup, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Email details frame
        details_frame = ttk.LabelFrame(main_frame, text="Email Details", padding="5")
        details_frame.pack(fill=tk.X, pady=(0, 10))

        # Subject
        ttk.Label(details_frame, text="Subject:", font=('TkDefaultFont', 9, 'bold')).grid(row=0, column=0, sticky="w")
        subject_text = tk.Text(details_frame, height=2, wrap=tk.WORD, font=('TkDefaultFont', 9))
        subject_text.grid(row=0, column=1, sticky="ew", padx=(10, 0))
        subject_text.insert(tk.END, email_data.get('subject', 'N/A'))
        subject_text.config(state=tk.DISABLED)

        # Email body frame
        body_frame = ttk.LabelFrame(main_frame, text="Email Body", padding="5")
        body_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Email body
        body_text = scrolledtext.ScrolledText(body_frame, wrap=tk.WORD, font=('TkDefaultFont', 10))
        body_text.pack(fill=tk.BOTH, expand=True)
        body_text.insert(tk.END, email_data.get('email_body', 'N/A'))
        body_text.config(state=tk.DISABLED)

        # Additional info frame
        info_frame = ttk.LabelFrame(main_frame, text="Additional Information", padding="5")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # Call to action
        ttk.Label(info_frame, text="Call to Action:", font=('TkDefaultFont', 9, 'bold')).grid(row=0, column=0, sticky="nw")
        cta_label = ttk.Label(info_frame, text=email_data.get('call_to_action', 'N/A'), wraplength=400)
        cta_label.grid(row=0, column=1, sticky="w", padx=(10, 0))

        # Deals offered
        ttk.Label(info_frame, text="Deals Offered:", font=('TkDefaultFont', 9, 'bold')).grid(row=1, column=0, sticky="nw")
        deals_label = ttk.Label(info_frame, text=email_data.get('deals_offered', 'N/A'), wraplength=400)
        deals_label.grid(row=1, column=1, sticky="w", padx=(10, 0))

        # Personalization notes
        ttk.Label(info_frame, text="Personalization:", font=('TkDefaultFont', 9, 'bold')).grid(row=2, column=0, sticky="nw")
        person_label = ttk.Label(info_frame, text=email_data.get('personalization_notes', 'N/A'), wraplength=400)
        person_label.grid(row=2, column=1, sticky="w", padx=(10, 0))

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # Copy to clipboard button
        def copy_email():
            email_content = f"Subject: {email_data.get('subject', '')}\n\n{email_data.get('email_body', '')}"
            popup.clipboard_clear()
            popup.clipboard_append(email_content)
            messagebox.showinfo("Copied", "Email copied to clipboard!")

        ttk.Button(button_frame, text="📋 Copy Email", command=copy_email).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Close", command=popup.destroy).pack(side=tk.RIGHT)

        # Configure grid weights
        details_frame.columnconfigure(1, weight=1)
        info_frame.columnconfigure(1, weight=1)

    def show_lead_details(self):
        """Show detailed lead information in a popup with action buttons"""
        if self.selected_row_index is None or self.current_df is None:
            messagebox.showwarning("No Selection", "Please select a row to view details.")
            return

        row_data = self.current_df.iloc[self.selected_row_index]

        # Create popup window
        popup = tk.Toplevel(self.root)
        popup.title("Lead Details")
        popup.geometry("900x700")
        popup.transient(self.root)
        popup.grab_set()

        # Main frame with scrollbar
        main_frame = ttk.Frame(popup, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create canvas and scrollbar for scrolling
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Header with lead name and company
        header_frame = ttk.Frame(scrollable_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Get lead name and company
        contact_name = f"{row_data.get('first_name', '')} {row_data.get('last_name', '')}".strip()
        if not contact_name:
            contact_name = row_data.get('full_name', 'Unknown Contact')
        company_name = row_data.get('company', 'Unknown Company')

        ttk.Label(header_frame, text=f"{contact_name}", font=('TkDefaultFont', 14, 'bold')).pack()
        ttk.Label(header_frame, text=f"{company_name}", font=('TkDefaultFont', 12)).pack()
        ttk.Label(header_frame, text=f"Row {self.selected_row_index + 1}", font=('TkDefaultFont', 10)).pack()

        # Action buttons frame
        action_frame = ttk.LabelFrame(scrollable_frame, text="Actions", padding="10")
        action_frame.pack(fill=tk.X, pady=(0, 20))

        # Check data availability for buttons
        has_website = pd.notna(row_data.get('website_url', '')) and row_data.get('website_url', '') != ''
        has_linkedin = pd.notna(row_data.get('linkedin_url', '')) and row_data.get('linkedin_url', '') != ''
        has_enriched_data = (pd.notna(row_data.get('ai_company_summary', '')) and
                           row_data.get('ai_company_summary', '') != '')

        # Create action buttons
        btn_frame = ttk.Frame(action_frame)
        btn_frame.pack()

        enrich_btn = ttk.Button(btn_frame, text="🔍 Enrich This Lead",
                               command=lambda: self.enrich_lead_from_popup(popup, row_data))
        enrich_btn.pack(side=tk.LEFT, padx=(0, 10))

        email_btn = ttk.Button(btn_frame, text="📧 Generate Sales Email",
                              command=lambda: self.generate_email_from_popup(popup, row_data))
        email_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Enable/disable buttons based on data
        if not (has_website or has_linkedin):
            enrich_btn.config(state="disabled")
        if not has_enriched_data:
            email_btn.config(state="disabled")

        # Status label
        status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(action_frame, textvariable=status_var)
        status_label.pack(pady=(10, 0))

        # Store references for updating from popup actions
        popup.status_var = status_var
        popup.enrich_btn = enrich_btn
        popup.email_btn = email_btn

        # Contact Information Section
        contact_frame = ttk.LabelFrame(scrollable_frame, text="Contact Information", padding="10")
        contact_frame.pack(fill=tk.X, pady=(0, 10))

        contact_fields = [
            ('First Name', 'first_name'),
            ('Last Name', 'last_name'),
            ('Full Name', 'full_name'),
            ('Title', 'title'),
            ('Email', 'email'),
            ('Phone', 'phone'),
            ('Location', 'location')
        ]

        self.create_detail_section(contact_frame, contact_fields, row_data)

        # Company Information Section
        company_frame = ttk.LabelFrame(scrollable_frame, text="Company Information", padding="10")
        company_frame.pack(fill=tk.X, pady=(0, 10))

        company_fields = [
            ('Company', 'company'),
            ('Industry', 'industry'),
            ('Website URL', 'website_url'),
            ('Revenue', 'revenue'),
            ('Employees', 'employees'),
            ('Description', 'description')
        ]

        self.create_detail_section(company_frame, company_fields, row_data)

        # Social Media Section
        social_frame = ttk.LabelFrame(scrollable_frame, text="Social Media", padding="10")
        social_frame.pack(fill=tk.X, pady=(0, 10))

        social_fields = [
            ('LinkedIn URL', 'linkedin_url'),
            ('Facebook URL', 'facebook_url'),
            ('Twitter URL', 'twitter_url'),
            ('Instagram URL', 'instagram_url')
        ]

        self.create_detail_section(social_frame, social_fields, row_data)

        # AI Enrichment Section (if available)
        ai_fields = [
            ('Company Summary', 'ai_company_summary'),
            ('Business Insights', 'ai_business_insights'),
            ('Key Products/Services', 'ai_key_products_services'),
            ('Target Market', 'ai_target_market'),
            ('Sales Opportunities', 'ai_sales_opportunities'),
            ('Competitive Advantages', 'ai_competitive_advantages'),
            ('Enrichment Status', 'enrichment_status'),
            ('Website Scraped At', 'website_scraped_at')
        ]

        # Check if any AI fields have data
        has_ai_data = any(pd.notna(row_data.get(field[1], '')) and row_data.get(field[1], '') != ''
                         for field in ai_fields)

        if has_ai_data:
            ai_frame = ttk.LabelFrame(scrollable_frame, text="AI Enrichment Data", padding="10")
            ai_frame.pack(fill=tk.X, pady=(0, 10))
            self.create_detail_section(ai_frame, ai_fields, row_data)

        # LinkedIn Enrichment Section (if available)
        linkedin_fields = [
            ('LinkedIn Full Name', 'linkedin_full_name'),
            ('LinkedIn Headline', 'linkedin_headline'),
            ('LinkedIn About', 'linkedin_about'),
            ('LinkedIn Location', 'linkedin_location'),
            ('LinkedIn Current Company', 'linkedin_current_company'),
            ('LinkedIn Current Title', 'linkedin_current_title'),
            ('LinkedIn Experience Summary', 'linkedin_experience_summary'),
            ('LinkedIn Education Summary', 'linkedin_education_summary'),
            ('LinkedIn Enrichment Status', 'linkedin_enrichment_status'),
            ('LinkedIn Scraped At', 'linkedin_scraped_at')
        ]

        # Check if any LinkedIn fields have data
        has_linkedin_data = any(pd.notna(row_data.get(field[1], '')) and row_data.get(field[1], '') != ''
                               for field in linkedin_fields)

        if has_linkedin_data:
            linkedin_frame = ttk.LabelFrame(scrollable_frame, text="LinkedIn Enrichment Data", padding="10")
            linkedin_frame.pack(fill=tk.X, pady=(0, 10))
            self.create_detail_section(linkedin_frame, linkedin_fields, row_data)

        # Close button
        close_frame = ttk.Frame(scrollable_frame)
        close_frame.pack(fill=tk.X, pady=(20, 0))
        ttk.Button(close_frame, text="Close", command=popup.destroy).pack()

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Cleanup mousewheel binding when popup closes
        def on_popup_close():
            canvas.unbind_all("<MouseWheel>")
            popup.destroy()

        popup.protocol("WM_DELETE_WINDOW", on_popup_close)

    def create_detail_section(self, parent_frame, fields, row_data):
        """Create a section with field labels and values"""
        for i, (label, field_name) in enumerate(fields):
            value = row_data.get(field_name, '')
            if pd.isna(value):
                value = ''
            value = str(value)

            # Skip empty fields to reduce clutter
            if not value.strip():
                continue

            # Create label
            label_widget = ttk.Label(parent_frame, text=f"{label}:", font=('TkDefaultFont', 9, 'bold'))
            label_widget.grid(row=i, column=0, sticky="nw", padx=(0, 10), pady=2)

            # Create value (with text wrapping for long values)
            if len(value) > 100:
                # Use Text widget for long values
                text_widget = tk.Text(parent_frame, height=3, wrap=tk.WORD, font=('TkDefaultFont', 9))
                text_widget.grid(row=i, column=1, sticky="ew", pady=2)
                text_widget.insert(tk.END, value)
                text_widget.config(state=tk.DISABLED)
            else:
                # Use Label for short values
                value_widget = ttk.Label(parent_frame, text=value, font=('TkDefaultFont', 9), wraplength=400)
                value_widget.grid(row=i, column=1, sticky="w", pady=2)

        # Configure column weights
        parent_frame.columnconfigure(1, weight=1)

    def enrich_lead_from_popup(self, popup, row_data):
        """Enrich lead from the popup window"""
        popup.status_var.set("Enriching...")
        popup.update()

        try:
            # Initialize enrichment tool if needed
            if self.enrichment_tool is None:
                if not self.openai_api_key:
                    popup.status_var.set("Error: No OpenAI API key")
                    return

                self.enrichment_tool = LeadEnrichmentTool(self.openai_api_key, self.apify_api_key)

            # Run enrichment
            enriched_data = asyncio.run(self.enrichment_tool.enrich_single_lead(row_data))

            if enriched_data and self.current_df is not None:
                # Update the dataframe
                for key, value in enriched_data.items():
                    if key not in self.current_df.columns:
                        self.current_df[key] = ''
                    self.current_df.at[self.selected_row_index, key] = value

                # Save to file
                if self.current_file:
                    self.current_df.to_csv(self.current_file, index=False)

                # Refresh main display
                self.load_csv_data(self.current_file)

                # Update popup buttons
                has_enriched_data = (pd.notna(enriched_data.get('ai_company_summary', '')) and
                                   enriched_data.get('ai_company_summary', '') != '')
                if has_enriched_data:
                    popup.email_btn.config(state="normal")

                popup.status_var.set("Enrichment completed!")

                # Close and reopen popup to show new data
                popup.destroy()
                self.show_lead_details()

            else:
                popup.status_var.set("Enrichment failed")

        except Exception as e:
            popup.status_var.set(f"Error: {str(e)[:50]}...")

    def generate_email_from_popup(self, popup, row_data):
        """Generate email from the popup window"""
        popup.status_var.set("Generating email...")
        popup.update()

        try:
            # Initialize email generator if needed
            if self.email_generator is None:
                if not self.openai_api_key:
                    popup.status_var.set("Error: No OpenAI API key")
                    return

                self.email_generator = SalesEmailGenerator(self.openai_api_key)

            # Generate email
            email_data = self.email_generator.generate_email_for_lead(row_data)

            popup.status_var.set("Email generated!")

            # Show email in popup (this will open on top of the details popup)
            self.show_email_popup(email_data)

        except Exception as e:
            popup.status_var.set(f"Error: {str(e)[:50]}...")

def main():
    """Main function to run the CSV viewer"""
    root = tk.Tk()
    app = CSVViewer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
