#!/usr/bin/env python3
"""
Sales Email Generator Module
Uses AI to create personalized sales emails based on enriched lead data
"""

import pandas as pd
import json
import logging
from typing import Dict, Optional, List
from datetime import datetime
from openai import OpenAI

# Configure logging
logger = logging.getLogger(__name__)

class SalesEmailGenerator:
    """Class for generating personalized sales emails using AI"""
    
    def __init__(self, openai_api_key: str):
        """Initialize the email generator with OpenAI API key"""
        self.openai_client = OpenAI(api_key=openai_api_key)
        self.company_info = self.get_company_info()
        self.product_info = self.get_product_info()
        
    def get_company_info(self) -> Dict[str, str]:
        """Get The Good Coffee Company information"""
        return {
            "company_name": "The Good Coffee Company",
            "address": "125 Hillvue Lane, Pittsburgh, PA 15237",
            "phone": "************",
            "email": "<EMAIL>",
            "website": "www.thegoodcoffeeco.com",
            "business_type": "Wholesale Coffee Bean Supplier",
            "location": "Pittsburgh, PA",
            "value_proposition": "Premium quality coffee beans sourced from the finest regions, delivered fresh to your business"
        }
    
    def get_product_info(self) -> Dict[str, any]:
        """Get product information from the invoice"""
        return {
            "products": [
                {"name": "Medium Colombian Whole Bean", "price": "$10.99"},
                {"name": "Medium Colombian Ground", "price": "$10.99"},
                {"name": "Dark Colombian Whole Bean", "price": "$10.99"},
                {"name": "Dark Colombian Ground", "price": "$10.99"},
                {"name": "Medium Nicaraguan Whole Bean", "price": "$10.99"},
                {"name": "Medium Nicaraguan Ground", "price": "$10.99"},
                {"name": "Dark Nicaraguan Whole Bean", "price": "$10.99"},
                {"name": "Dark Nicaraguan Ground", "price": "$10.99"}
            ],
            "origins": ["Colombian", "Nicaraguan"],
            "roast_levels": ["Medium", "Dark"],
            "formats": ["Whole Bean", "Ground"],
            "pricing": "Competitive wholesale pricing starting at $10.99",
            "payment_methods": "Online Invoice or Check",
            "specialties": [
                "Premium single-origin beans",
                "Fresh roasted to order",
                "Consistent quality and flavor profiles",
                "Flexible ordering and delivery",
                "Local Pittsburgh supplier"
            ],
            "authorized_deals": {
                "coffee_shops": {
                    "first_order_discount": "15% off first order",
                    "volume_discount": "Additional 5% off orders over 50 lbs",
                    "trial_offer": "Free 2 lb sample pack",
                    "loyalty_program": "Buy 10 bags, get 1 free",
                    "payment_terms": "Net 30 payment terms for established accounts"
                },
                "restaurants": {
                    "bulk_pricing": "Special bulk pricing for dining service (20+ lbs)",
                    "custom_blend": "Free custom blend development for your restaurant",
                    "catering_package": "Catering coffee service packages available",
                    "trial_program": "Free 1-week trial of our coffee service",
                    "branded_options": "Custom packaging with your restaurant branding",
                    "delivery_service": "Regular scheduled delivery service"
                },
                "retail_stores": {
                    "placement_incentive": "Free initial inventory (up to $500 value)",
                    "marketing_support": "Free point-of-sale materials and shelf talkers",
                    "volume_pricing": "Tiered pricing based on monthly volume commitments",
                    "exclusive_territory": "Potential exclusive territory rights for high-volume partners",
                    "promotional_support": "Co-op advertising opportunities",
                    "demo_program": "Free in-store sampling events"
                },
                "general": {
                    "new_customer": "10% off first order",
                    "bulk_discount": "5% off orders over $200",
                    "seasonal_promotion": "Special holiday blend offerings",
                    "referral_bonus": "$50 credit for successful referrals"
                }
            }
        }
    
    def extract_lead_data(self, row: pd.Series) -> Dict[str, str]:
        """Extract and organize all available lead data"""
        lead_data = {
            # Basic contact info
            "first_name": row.get('first_name', ''),
            "last_name": row.get('last_name', ''),
            "full_name": row.get('full_name', ''),
            "email": row.get('email', ''),
            "title": row.get('title', ''),
            "company": row.get('company', ''),
            "industry": row.get('industry', ''),
            "website_url": row.get('website_url', ''),
            "phone": row.get('phone', ''),
            "location": row.get('location', ''),
            
            # Company details
            "company_description": row.get('description', ''),
            "revenue": row.get('revenue', ''),
            "employees": row.get('employees', ''),
            
            # Website enrichment data
            "ai_company_summary": row.get('ai_company_summary', ''),
            "ai_business_insights": row.get('ai_business_insights', ''),
            "ai_key_products_services": row.get('ai_key_products_services', ''),
            "ai_target_market": row.get('ai_target_market', ''),
            "ai_sales_opportunities": row.get('ai_sales_opportunities', ''),
            "ai_competitive_advantages": row.get('ai_competitive_advantages', ''),
            
            # LinkedIn enrichment data
            "linkedin_full_name": row.get('linkedin_full_name', ''),
            "linkedin_headline": row.get('linkedin_headline', ''),
            "linkedin_about": row.get('linkedin_about', ''),
            "linkedin_location": row.get('linkedin_location', ''),
            "linkedin_current_company": row.get('linkedin_current_company', ''),
            "linkedin_current_title": row.get('linkedin_current_title', ''),
            "linkedin_experience_summary": row.get('linkedin_experience_summary', ''),
            "linkedin_education_summary": row.get('linkedin_education_summary', ''),
        }
        
        return lead_data

    def determine_sales_approach(self, lead_data: Dict[str, str]) -> Dict[str, str]:
        """Intelligently determine the sales approach based on lead data"""

        # Helper function to safely convert to lowercase string
        def safe_lower(value):
            if value is None or pd.isna(value):
                return ''
            return str(value).lower()

        company = safe_lower(lead_data.get('company', ''))
        industry = safe_lower(lead_data.get('industry', ''))
        title = safe_lower(lead_data.get('linkedin_current_title') or lead_data.get('title', ''))
        business_insights = safe_lower(lead_data.get('ai_business_insights', ''))
        company_summary = safe_lower(lead_data.get('ai_company_summary', ''))
        products_services = safe_lower(lead_data.get('ai_key_products_services', ''))

        # Coffee shop indicators
        coffee_shop_keywords = [
            'coffee shop', 'coffee house', 'coffeehouse', 'cafe', 'espresso bar',
            'roastery', 'coffee roaster', 'coffee company', 'coffee roasting',
            'barista', 'coffee beans', 'espresso', 'latte', 'cappuccino'
        ]

        # Restaurant indicators
        restaurant_keywords = [
            'restaurant', 'dining', 'bistro', 'eatery', 'grill', 'tavern', 'pub',
            'diner', 'steakhouse', 'pizzeria', 'bakery', 'hotel', 'hospitality',
            'catering', 'banquet', 'event center', 'country club', 'resort'
        ]

        # Retail store indicators
        retail_keywords = [
            'grocery', 'supermarket', 'retail', 'store', 'market', 'food service',
            'food distribution', 'wholesale', 'distributor', 'chain', 'franchise',
            'convenience store', 'gas station', 'pharmacy'
        ]

        # Decision maker titles for retail
        retail_titles = [
            'buyer', 'purchasing', 'procurement', 'category manager', 'merchandising',
            'vendor relations', 'supplier relations', 'product manager', 'brand manager'
        ]

        # Check for coffee shop approach
        coffee_shop_score = 0
        for keyword in coffee_shop_keywords:
            if (keyword in company or keyword in industry or
                keyword in business_insights or keyword in company_summary or
                keyword in products_services):
                coffee_shop_score += 1

        # Check for restaurant approach
        restaurant_score = 0
        for keyword in restaurant_keywords:
            if (keyword in company or keyword in industry or
                keyword in business_insights or keyword in company_summary or
                keyword in products_services):
                restaurant_score += 1

        # Check for retail approach
        retail_score = 0
        for keyword in retail_keywords:
            if (keyword in company or keyword in industry or
                keyword in business_insights or keyword in company_summary):
                retail_score += 1

        # Title-based scoring
        for retail_title in retail_titles:
            if retail_title in title:
                retail_score += 2  # Higher weight for titles

        # Determine approach based on highest score
        scores = {
            "coffee_shop": coffee_shop_score,
            "restaurant": restaurant_score,
            "retail_placement": retail_score
        }

        max_score = max(scores.values())
        if max_score == 0:
            # Default to general business approach
            approach = "general_business"
            strategy = "consultative"
            focus = "value_and_service"
        else:
            # Get the approach with the highest score
            approach = max(scores, key=scores.get)

            if approach == "coffee_shop":
                strategy = "direct_sales"
                focus = "quality_and_freshness"
            elif approach == "restaurant":
                strategy = "beverage_program"
                focus = "customer_experience_and_bulk"
            elif approach == "retail_placement":
                strategy = "partnership"
                focus = "profit_margins_and_support"

        return {
            "approach": approach,
            "strategy": strategy,
            "focus": focus,
            "coffee_shop_score": coffee_shop_score,
            "restaurant_score": restaurant_score,
            "retail_score": retail_score,
            "reasoning": f"Coffee shop: {coffee_shop_score}, Restaurant: {restaurant_score}, Retail: {retail_score}"
        }
    
    def generate_sales_email(self, lead_data: Dict[str, str]) -> Dict[str, str]:
        """Generate a personalized sales email using AI"""
        try:
            # Determine the intelligent sales approach
            sales_approach = self.determine_sales_approach(lead_data)

            # Create a comprehensive prompt with all available data
            prompt = self.create_email_prompt(lead_data, sales_approach)

            logger.info(f"Generating {sales_approach['approach']} sales email for {lead_data.get('company', 'Unknown Company')}")

            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": self.get_system_prompt(sales_approach)},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.7  # Slightly higher for more creative emails
            )
            
            # Parse the JSON response
            ai_response = response.choices[0].message.content
            if ai_response:
                ai_response = ai_response.strip()
            else:
                ai_response = ""
            
            # Try to extract JSON from the response
            try:
                # Remove any markdown formatting
                if ai_response.startswith('```json'):
                    ai_response = ai_response[7:]
                if ai_response.endswith('```'):
                    ai_response = ai_response[:-3]
                
                email_data = json.loads(ai_response)
                logger.info(f"Successfully generated sales email for {lead_data.get('company', 'Unknown Company')}")
                return email_data
                
            except json.JSONDecodeError:
                logger.warning(f"Failed to parse JSON response for {lead_data.get('company', 'Unknown Company')}, using fallback")
                # Fallback: create a basic structure with the raw response
                return {
                    "subject": f"Premium Coffee Solutions for {lead_data.get('company', 'Your Business')}",
                    "email_body": ai_response,
                    "call_to_action": "I'd love to schedule a brief call to discuss how we can support your coffee needs.",
                    "personalization_notes": "AI-generated email with available lead data",
                    "recommended_follow_up": "Follow up in 3-5 business days if no response"
                }
                
        except Exception as e:
            logger.error(f"Error generating sales email for {lead_data.get('company', 'Unknown Company')}: {e}")
            return self.create_fallback_email(lead_data)
    
    def get_system_prompt(self, sales_approach: Dict[str, str]) -> str:
        """Get the context-aware system prompt for the AI"""

        approach = sales_approach.get('approach', 'general_business')

        base_prompt = """You are an expert B2B sales email writer specializing in wholesale coffee sales. You use ADVANCED SALES PSYCHOLOGY and write SHORT, TACTICAL emails that:

1. Start with a PSYCHOLOGICAL HOOK that triggers curiosity or creates a knowledge gap
2. Use SCARCITY and URGENCY principles to create desire
3. Apply SOCIAL PROOF and AUTHORITY to build credibility
4. Leverage RECIPROCITY by offering value upfront
5. Create FOMO (Fear of Missing Out) around opportunities
6. Use PATTERN INTERRUPTS to stand out from typical sales emails
7. Apply LOSS AVERSION - what they're missing by not having premium coffee
8. Include LIGHT HUMOR to build rapport and memorability
9. Are concise but psychologically impactful (150-250 words maximum)
10. Have a DIRECT, URGENT call-to-action using ASSUMPTIVE CLOSE techniques

TACTICAL EMAIL STRUCTURE (Sales Psychology):
1. PSYCHOLOGICAL HOOK (1 sentence): Create curiosity gap or pattern interrupt
2. SCARCITY/URGENCY (1 sentence): Limited time or exclusive opportunity
3. SOCIAL PROOF + HUMOR (1-2 sentences): Others like them + light coffee joke
4. LOSS AVERSION (1-2 sentences): What they're missing without premium coffee
5. RECIPROCITY/VALUE (1-2 sentences): Free offer or valuable insight
6. ASSUMPTIVE CLOSE (2 sentences): Direct scheduling with urgency

SALES PSYCHOLOGY TACTICS TO USE:
- CURIOSITY GAP: "Most [industry] owners don't know this about coffee..."
- SCARCITY: "Only working with 3 new [industry] partners this month"
- SOCIAL PROOF: "Just helped [similar business] increase [specific metric]"
- LOSS AVERSION: "Your competitors are already using premium coffee to..."
- RECIPROCITY: "I'll send you our [free resource] regardless of whether we work together"
- AUTHORITY: "15 years helping [industry] businesses with coffee solutions"
- FOMO: "The [specific benefit] opportunity won't last long"
- PATTERN INTERRUPT: Unexpected opening that breaks their mental pattern

HOOK REQUIREMENTS:
- Create CURIOSITY or knowledge gap they need to fill
- Use PATTERN INTERRUPTS to stand out from typical sales emails
- Reference SPECIFIC business situations that create urgency
- Use psychological triggers: "Most people don't know...", "Here's what your competitors are doing...", "Quick question that might surprise you..."

HUMOR GUIDELINES:
- Keep it LIGHT and coffee-related when possible
- Make it relevant to their business/industry
- Examples: "I promise this isn't another 'grounds for improvement' pitch" or "Unlike your morning coffee, this won't keep you up at night"
- Avoid anything that could be offensive or too casual

PERSONALIZATION STYLE:
- Weave in data NATURALLY with a conversational tone
- Use their business context to frame coffee benefits
- Reference their industry/role casually
- Connect location for local relevance
- Use their business terminology in a friendly way

CALL-TO-ACTION REQUIREMENTS:
- Be DIRECT: "Let's schedule a 15-minute call" NOT "Would you be available"
- Offer SPECIFIC times: "I have Tuesday at 2pm or Wednesday at 10am available"
- Create URGENCY: "This week only" or "Limited time offer"
- Be ASSUMPTIVE: "I'll send you a calendar link" NOT "if you're interested"
- Include PHONE for immediate contact: "Call me directly at ************"

CRITICAL: Keep it SHORT, SMOOTH, and FUN while being professional. Make them smile!
"""

        if approach == "coffee_shop":
            specific_guidance = """

COFFEE SHOP APPROACH - You are selling directly to coffee shops/cafes:
- Focus on product quality, freshness, and flavor profiles
- Emphasize local sourcing and quick delivery from Pittsburgh
- Highlight competitive pricing and flexible ordering
- Offer coffee shop specific deals: 15% off first order, volume discounts, free samples
- Position as a reliable supplier partner for their daily operations
- Mention support for their customer experience and coffee quality"""

        elif approach == "restaurant":
            specific_guidance = """

RESTAURANT APPROACH - You are selling to restaurants/dining establishments:
- Focus on enhancing customer dining experience and beverage programs
- Emphasize bulk pricing for dining service and custom blend development
- Highlight consistent quality for customer satisfaction
- Offer restaurant-specific deals: bulk pricing, custom blends, catering packages, branded options
- Position as a partner to elevate their beverage program
- Mention delivery service and trial programs"""

        elif approach == "retail_placement":
            specific_guidance = """

RETAIL PLACEMENT APPROACH - You are pitching for shelf space in retail stores:
- Focus on profit margins, turnover rates, and customer demand
- Emphasize marketing support, POS materials, and promotional opportunities
- Highlight brand quality and customer appeal
- Offer retail-specific incentives: free initial inventory, marketing support, exclusive territory rights
- Position as a profitable product line addition
- Mention co-op advertising and in-store demo opportunities"""

        else:
            specific_guidance = """

GENERAL BUSINESS APPROACH - Consultative sales approach:
- Focus on understanding their specific coffee needs
- Emphasize quality, service, and value proposition
- Offer general incentives: new customer discounts, bulk pricing
- Position as a flexible coffee solutions partner"""

        return base_prompt + specific_guidance + """

Always respond with valid JSON in this exact format:
{
    "subject": "Compelling subject line (under 60 characters)",
    "email_body": "Full email body with proper formatting and line breaks",
    "call_to_action": "Specific call-to-action sentence",
    "personalization_notes": "Key personalization elements used",
    "recommended_follow_up": "Follow-up strategy recommendation",
    "deals_offered": "List any specific deals or incentives mentioned"
}

Focus on building relationships and providing value, not just selling products."""
    
    def create_email_prompt(self, lead_data: Dict[str, str], sales_approach: Dict[str, str]) -> str:
        """Create a detailed prompt for email generation with maximum personalization"""

        # Helper function to safely get string value
        def safe_str(value):
            if value is None or pd.isna(value):
                return ''
            return str(value).strip()

        # Extract all available contact information
        linkedin_name = safe_str(lead_data.get('linkedin_full_name'))
        full_name = safe_str(lead_data.get('full_name'))
        first_name = safe_str(lead_data.get('first_name'))
        last_name = safe_str(lead_data.get('last_name'))

        contact_name = linkedin_name or full_name or f"{first_name} {last_name}".strip()
        if not contact_name:
            contact_name = "there"  # Fallback greeting

        # Get the most detailed title information
        linkedin_title = safe_str(lead_data.get('linkedin_current_title'))
        title = safe_str(lead_data.get('title'))
        linkedin_headline = safe_str(lead_data.get('linkedin_headline'))
        contact_title = linkedin_title or title or linkedin_headline

        # Get the most detailed company information
        linkedin_company = safe_str(lead_data.get('linkedin_current_company'))
        company = safe_str(lead_data.get('company'))
        company_name = linkedin_company or company

        # Extract location information for local connection
        linkedin_location = safe_str(lead_data.get('linkedin_location'))
        location = safe_str(lead_data.get('location'))
        contact_location = linkedin_location or location

        # Extract professional background for personalization
        linkedin_about = safe_str(lead_data.get('linkedin_about'))
        linkedin_experience = safe_str(lead_data.get('linkedin_experience_summary'))
        linkedin_education = safe_str(lead_data.get('linkedin_education_summary'))

        # Extract company insights for deep personalization
        company_description = safe_str(lead_data.get('description'))
        industry = safe_str(lead_data.get('industry'))
        revenue = safe_str(lead_data.get('revenue'))
        employees = safe_str(lead_data.get('employees'))
        
        prompt = f"""
Write a HIGHLY PERSONALIZED sales email for The Good Coffee Company (wholesale coffee bean supplier in Pittsburgh, PA) to the following prospect.

USE ALL AVAILABLE DATA to create maximum personalization and relevance:

PROSPECT CONTACT DETAILS:
- Name: {contact_name}
- Title: {contact_title}
- Company: {company_name}
- Industry: {industry}
- Location: {contact_location}
- Revenue: {revenue}
- Employees: {employees}

DEEP COMPANY INTELLIGENCE (Use extensively for personalization):
- Company Summary: {lead_data.get('ai_company_summary', 'Not available')}
- Business Insights: {lead_data.get('ai_business_insights', 'Not available')}
- Key Products/Services: {lead_data.get('ai_key_products_services', 'Not available')}
- Target Market: {lead_data.get('ai_target_market', 'Not available')}
- Sales Opportunities: {lead_data.get('ai_sales_opportunities', 'Not available')}
- Competitive Advantages: {lead_data.get('ai_competitive_advantages', 'Not available')}
- Company Description: {company_description}

LINKEDIN PROFESSIONAL PROFILE (Use for personal connection):
- Professional Headline: {linkedin_headline}
- About Section: {linkedin_about[:300] if linkedin_about else 'Not available'}
- Professional Experience: {linkedin_experience}
- Education Background: {linkedin_education}
- Current Role Details: {linkedin_title} at {linkedin_company}

PERSONALIZATION REQUIREMENTS:
1. Reference SPECIFIC details from their company summary and business insights
2. Mention their SPECIFIC products/services and how coffee fits into their operation
3. Reference their TARGET MARKET and how quality coffee enhances customer experience
4. Use their COMPETITIVE ADVANTAGES to show how premium coffee supports their brand
5. If location data available, mention LOCAL Pittsburgh connection and delivery benefits
6. Reference their PROFESSIONAL BACKGROUND from LinkedIn to build rapport
7. Connect their INDUSTRY EXPERIENCE to coffee needs (restaurants need dining coffee, retail needs shelf products, etc.)
8. Use COMPANY SIZE (employees/revenue) to suggest appropriate volume and pricing
9. Reference any SPECIFIC BUSINESS CHALLENGES mentioned in insights and position coffee as a solution

OUR COMPANY - THE GOOD COFFEE COMPANY:
- Location: Pittsburgh, PA (125 Hillvue Lane, Pittsburgh, PA 15237)
- Products: Premium Colombian and Nicaraguan coffee beans
- Formats: Whole bean and ground, Medium and Dark roasts
- Pricing: Wholesale pricing starting at $10.99
- Value Prop: Fresh roasted, consistent quality, local supplier
- Contact: <EMAIL>, ************

SALES APPROACH ANALYSIS:
- Determined Approach: {sales_approach.get('approach', 'general_business')}
- Strategy: {sales_approach.get('strategy', 'consultative')}
- Focus Area: {sales_approach.get('focus', 'value_and_service')}
- Reasoning: {sales_approach.get('reasoning', 'Standard business approach')}

AUTHORIZED DEALS & INCENTIVES:
{self.get_relevant_deals(sales_approach.get('approach', 'general_business'))}

EMAIL REQUIREMENTS - SHORT, PUNCHY & FUN:
1. ENGAGING INTRO (1-2 sentences): Warm greeting + specific business reference
2. LIGHT HUMOR (1 sentence): Coffee joke or playful industry comment
3. TRANSITION TO OFFERING (1-2 sentences): Connect their situation to our coffee solution
4. VALUE PROPOSITION (2-3 sentences): How we specifically help their business succeed
5. CREDIBILITY (1 sentence): Brief social proof or local connection
6. DIRECT CALL-TO-ACTION (2 sentences): Assumptive close with specific times
7. Keep it SHORT: 150-250 words maximum
8. Make them SMILE while being professional

PSYCHOLOGICAL HOOK EXAMPLES (Sales Psychology):
- "[Name], most [industry] owners don't know this about their coffee costs..."
- "Quick question that might surprise you about [company]'s profit margins..."
- "[Name], your competitors are doing something with coffee that you're not..."
- "Here's what 73% of successful [industry] businesses have in common..."
- "[Name], I noticed something about [company] that could be costing you customers..."
- "Most people in [industry] make this one expensive coffee mistake..."
- "[Name], there's a reason your customers aren't staying longer..."
- "Quick reality check about [company]'s beverage program..."

COFFEE HUMOR EXAMPLES:
- "I promise this isn't another 'grounds for improvement' pitch!"
- "Unlike your morning coffee, this opportunity won't keep you up at night."
- "No need to espresso any concerns - this is actually good news!"
- "I'm not here to give you a latte problems, just solutions."
- "This might be more exciting than your first cup of coffee today."
- "Don't worry, I won't be brewing up any trouble for you."

INDUSTRY-SPECIFIC PSYCHOLOGICAL TACTICS:

RESTAURANTS (Loss Aversion + Social Proof):
- "Most restaurant owners don't realize their coffee is costing them repeat customers..."
- "Your competitors are using premium coffee to increase average ticket by 23%..."
- "Here's why 67% of diners judge restaurants by their coffee quality..."

RETAIL (FOMO + Authority):
- "Your coffee aisle could be generating 40% higher margins than it currently is..."
- "Most retailers miss this one opportunity that coffee shops figured out years ago..."
- "There's a reason successful stores are switching to premium wholesale coffee..."

COFFEE SHOPS (Pattern Interrupt + Scarcity):
- "I know what you're thinking - another coffee supplier. But here's what's different..."
- "Most coffee shop owners make this one costly sourcing mistake..."
- "Only working with 2 new coffee shops this quarter, and here's why..."

PSYCHOLOGICAL TRIGGERS TO WEAVE IN:
- SCARCITY: "Only 3 spots left for new partners this month"
- SOCIAL PROOF: "Just helped [similar business] increase [metric] by X%"
- AUTHORITY: "15 years helping [industry] optimize coffee programs"
- LOSS AVERSION: "Every day without premium coffee is costing you customers"
- RECIPROCITY: "I'll send you our profit analysis tool regardless"
- CURIOSITY: "The one thing successful [industry] businesses do differently"

SMOOTH TRANSITIONS:
- "That's exactly why premium coffee could be a game-changer for your [specific operation]..."
- "We help businesses like yours enhance [specific customer experience] with..."
- "Our wholesale coffee program is designed for [their specific business model]..."

PSYCHOLOGICAL CALL-TO-ACTION EXAMPLES:
- "I'm only taking on 3 new partners this month - let's grab one of the remaining Tuesday 2pm or Wednesday 10am slots."
- "This opportunity expires Friday - I have two spots left: Tuesday 2pm or Wednesday 10am."
- "I'll send you our profit calculator regardless, but if you want the custom analysis, let's talk Tuesday 2pm or Wednesday 10am."
- "Most people wait and miss out - but you seem different. Tuesday 2pm or Wednesday 10am?"
- "I'm blocking off time for serious partners only - Tuesday 2pm or Wednesday 10am work?"

PSYCHOLOGICAL PRINCIPLES IN CTA:
- SCARCITY: Limited spots/time available
- URGENCY: Deadline pressure
- SOCIAL PROOF: "Most successful businesses..."
- RECIPROCITY: Free value regardless of outcome
- LOSS AVERSION: "Don't miss out like others"
- ASSUMPTIVE CLOSE: Assumes they want to meet
- BINARY CHOICE: Two specific options, not yes/no

Focus on PSYCHOLOGICAL TRIGGERS that compel action, not just politeness.
"""
        
        return prompt

    def get_relevant_deals(self, approach: str) -> str:
        """Get relevant deals based on the sales approach"""
        deals = self.product_info["authorized_deals"]

        if approach == "coffee_shop":
            relevant_deals = deals["coffee_shops"]
            deal_text = """Coffee Shop Deals (You are authorized to offer):
- First Order: 15% off first order (if they book a call this week)
- Volume Discount: Additional 5% off orders over 50 lbs
- Trial Offer: Free 2 lb sample pack (delivered within 48 hours of our call)
- Loyalty Program: Buy 10 bags, get 1 free
- Payment Terms: Net 30 payment terms for established accounts"""

        elif approach == "restaurant":
            relevant_deals = deals["restaurants"]
            deal_text = """Restaurant Partnership Deals (You are authorized to offer):
- Bulk Pricing: Special bulk pricing for dining service (20+ lbs)
- Custom Blend: Free custom blend development for your restaurant
- Catering Package: Catering coffee service packages available
- Trial Program: Free 1-week trial of our coffee service (if we meet this week)
- Branded Options: Custom packaging with your restaurant branding
- Delivery Service: Regular scheduled delivery service"""

        elif approach == "retail_placement":
            relevant_deals = deals["retail_stores"]
            deal_text = """Retail Partnership Incentives (You are authorized to offer):
- Placement Incentive: Free initial inventory (up to $500 value)
- Marketing Support: Free point-of-sale materials and shelf talkers
- Volume Pricing: Tiered pricing based on monthly volume commitments
- Territory Rights: Potential exclusive territory rights for high-volume partners
- Promotional Support: Co-op advertising opportunities
- Demo Program: Free in-store sampling events"""

        else:
            relevant_deals = deals["general"]
            deal_text = """General Business Incentives (You are authorized to offer):
- New Customer: 10% off first order
- Bulk Discount: 5% off orders over $200
- Seasonal: Special holiday blend offerings
- Referral Bonus: $50 credit for successful referrals"""

        return deal_text
    
    def create_fallback_email(self, lead_data: Dict[str, str]) -> Dict[str, str]:
        """Create a basic fallback email if AI generation fails"""
        # Helper function to safely get string value
        def safe_str(value):
            if value is None or pd.isna(value):
                return ''
            return str(value).strip()

        # Determine the best name to use
        linkedin_name = safe_str(lead_data.get('linkedin_full_name'))
        full_name = safe_str(lead_data.get('full_name'))
        first_name = safe_str(lead_data.get('first_name'))
        last_name = safe_str(lead_data.get('last_name'))

        contact_name = linkedin_name or full_name or f"{first_name} {last_name}".strip()
        if not contact_name or contact_name == "nan nan":
            contact_name = "there"  # Fallback greeting

        # Determine the best company to use
        linkedin_company = safe_str(lead_data.get('linkedin_current_company'))
        company = safe_str(lead_data.get('company'))
        company_name = linkedin_company or company or "your business"

        # Get title and location
        linkedin_title = safe_str(lead_data.get('linkedin_current_title'))
        title = safe_str(lead_data.get('title'))
        contact_title = linkedin_title or title

        linkedin_location = safe_str(lead_data.get('linkedin_location'))
        location = safe_str(lead_data.get('location'))
        contact_location = linkedin_location or location
        
        # Create location context for the email body
        location_context = ""
        if contact_location and "pittsburgh" in contact_location.lower():
            location_context = " Since you're also in the Pittsburgh area, we can offer same-day delivery and local support."
        elif contact_location:
            location_context = f" We ship nationwide and can easily serve your {contact_location} location."

        # Add some humor based on industry
        humor_line = "I promise this isn't another 'grounds for improvement' pitch!"
        if lead_data.get('industry', '').lower() in ['hospitality', 'restaurant']:
            humor_line = "Your customers already love your food - imagine if they raved about your coffee too!"
        elif 'retail' in lead_data.get('industry', '').lower():
            humor_line = "Your shelves deserve coffee that sells itself (unlike that one product that's been there since 2019)."
        elif 'coffee' in lead_data.get('company', '').lower():
            humor_line = "I know, I know - another coffee supplier. But hear me out..."

        # Create a better hook based on available data
        hook_line = f"Quick question about {company_name}'s operations..."
        if contact_title and 'manager' in contact_title.lower():
            hook_line = f"Hey {contact_name}! Bet you didn't expect a coffee email today."
        elif lead_data.get('industry', '').lower() in ['hospitality', 'restaurant']:
            hook_line = f"Hi {contact_name}! I have a theory about your after-dinner service..."
        elif 'retail' in lead_data.get('industry', '').lower():
            hook_line = f"{contact_name}, noticed {company_name} but wondering about one thing..."

        return {
            "subject": f"Coffee Partnership for {company_name}",
            "email_body": f"""{hook_line}

{humor_line}

We're The Good Coffee Company, a Pittsburgh-based wholesale supplier specializing in premium Colombian and Nicaraguan beans. Our fresh-roasted coffee and competitive pricing help businesses like yours enhance customer satisfaction and boost profits.{location_context}

Let's schedule a quick 15-minute call this week - I have Tuesday at 2pm or Wednesday at 10am available. I'd love to show you how premium coffee can add value to {company_name}.

Call me directly at ************ or reply with your preferred time.

Best regards,
The Good Coffee Company
************""",
            "call_to_action": "Let's schedule a quick 15-minute call this week - I have Tuesday at 2pm or Wednesday at 10am available.",
            "personalization_notes": f"Fun tone with industry-specific humor, name, title, company, industry{', and location' if contact_location else ''}",
            "recommended_follow_up": "Follow up in 3-5 business days with additional value proposition"
        }
    
    def generate_email_for_lead(self, row: pd.Series) -> Dict[str, str]:
        """Main method to generate email for a single lead"""
        lead_data = self.extract_lead_data(row)
        email_data = self.generate_sales_email(lead_data)
        
        # Add metadata
        email_data['generated_at'] = datetime.now().isoformat()

        # Helper function to safely get string value
        def safe_str(value):
            if value is None or pd.isna(value):
                return ''
            return str(value).strip()

        # Get clean company name
        linkedin_company = safe_str(lead_data.get('linkedin_current_company'))
        company = safe_str(lead_data.get('company'))
        email_data['lead_company'] = linkedin_company or company or "Unknown Company"

        # Get clean contact name
        linkedin_name = safe_str(lead_data.get('linkedin_full_name'))
        full_name = safe_str(lead_data.get('full_name'))
        first_name = safe_str(lead_data.get('first_name'))
        last_name = safe_str(lead_data.get('last_name'))

        contact_name = linkedin_name or full_name or f"{first_name} {last_name}".strip()
        if not contact_name or contact_name == "nan nan":
            contact_name = "Contact"  # Fallback name
        email_data['lead_name'] = contact_name
        
        return email_data


