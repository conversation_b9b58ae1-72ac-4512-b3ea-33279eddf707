#!/usr/bin/env python3
"""
LinkedIn Profile Enrichment Module
Uses Apify to scrape LinkedIn profiles and extract professional information
"""

import re
import logging
from typing import Dict, Optional, List
from urllib.parse import urlparse
from apify_client import ApifyClient
import time

# Configure logging
logger = logging.getLogger(__name__)

class LinkedInEnricher:
    """Class for enriching leads with LinkedIn profile data"""
    
    def __init__(self, apify_api_token: str):
        """Initialize the LinkedIn enricher with Apify API token"""
        self.client = ApifyClient(apify_api_token)
        self.actor_id = "VhxlqQXRwhW8H5hNV"  # LinkedIn profile detail actor
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        
        # New LinkedIn columns to add to CSV
        self.linkedin_columns = [
            'linkedin_full_name',
            'linkedin_headline', 
            'linkedin_about',
            'linkedin_location',
            'linkedin_current_company',
            'linkedin_current_title',
            'linkedin_follower_count',
            'linkedin_connection_count',
            'linkedin_experience_summary',
            'linkedin_education_summary',
            'linkedin_scraped_at',
            'linkedin_enrichment_status'
        ]
    
    def extract_linkedin_username(self, linkedin_url: str) -> Optional[str]:
        """Extract LinkedIn username from various LinkedIn URL formats"""
        if not linkedin_url or not isinstance(linkedin_url, str):
            return None
        
        # Clean the URL
        linkedin_url = linkedin_url.strip().strip('"').strip("'")
        
        # Common LinkedIn URL patterns
        patterns = [
            r'linkedin\.com/in/([^/?]+)',  # Standard profile URL
            r'linkedin\.com/pub/([^/?]+)',  # Public profile URL
            r'linkedin\.com/profile/view\?id=([^&]+)',  # Old format
        ]
        
        for pattern in patterns:
            match = re.search(pattern, linkedin_url, re.IGNORECASE)
            if match:
                username = match.group(1)
                # Clean username (remove trailing slashes, parameters)
                username = username.split('/')[0].split('?')[0].split('&')[0]
                return username
        
        logger.warning(f"Could not extract username from LinkedIn URL: {linkedin_url}")
        return None
    
    def scrape_linkedin_profile(self, username: str) -> Optional[Dict]:
        """Scrape LinkedIn profile using Apify"""
        try:
            logger.info(f"Scraping LinkedIn profile: {username}")
            
            # Prepare the Actor input
            run_input = {
                "username": username,
                "includeEmail": False,  # Don't include email for privacy
            }
            
            # Run the Actor and wait for it to finish
            run = self.client.actor(self.actor_id).call(run_input=run_input)
            
            if run['status'] != 'SUCCEEDED':
                logger.warning(f"LinkedIn scraping failed for {username}: {run.get('statusMessage', 'Unknown error')}")
                return None
            
            # Fetch results from the run's dataset
            results = []
            for item in self.client.dataset(run["defaultDatasetId"]).iterate_items():
                results.append(item)
                break  # We only need the first (and should be only) result
            
            if not results:
                logger.warning(f"No LinkedIn data returned for {username}")
                return None
            
            profile_data = results[0]
            logger.info(f"Successfully scraped LinkedIn profile for {username}")
            return profile_data
            
        except Exception as e:
            logger.error(f"Error scraping LinkedIn profile {username}: {e}")
            return None
    
    def process_linkedin_data(self, profile_data: Dict) -> Dict[str, str]:
        """Process raw LinkedIn data into structured format for CSV"""
        try:
            basic_info = profile_data.get('basic_info', {})
            experience = profile_data.get('experience', [])
            education = profile_data.get('education', [])
            
            # Extract current position (first experience entry that's current)
            current_title = ""
            current_company = ""
            for exp in experience:
                if exp.get('is_current', False):
                    current_title = exp.get('title', '')
                    current_company = exp.get('company', '')
                    break
            
            # Create experience summary (top 3 positions)
            experience_summary = []
            for i, exp in enumerate(experience[:3]):
                title = exp.get('title', '')
                company = exp.get('company', '')
                if title and company:
                    experience_summary.append(f"{title} at {company}")
            
            # Create education summary (top 2 entries)
            education_summary = []
            for i, edu in enumerate(education[:2]):
                school = edu.get('school', '')
                degree = edu.get('degree', '')
                if school:
                    if degree:
                        education_summary.append(f"{degree} from {school}")
                    else:
                        education_summary.append(school)
            
            # Structure the data for CSV
            linkedin_data = {
                'linkedin_full_name': basic_info.get('fullname', ''),
                'linkedin_headline': basic_info.get('headline', ''),
                'linkedin_about': (basic_info.get('about', '') or '')[:500],  # Limit length
                'linkedin_location': basic_info.get('location', {}).get('full', ''),
                'linkedin_current_company': current_company,
                'linkedin_current_title': current_title,
                'linkedin_follower_count': str(basic_info.get('follower_count', '')),
                'linkedin_connection_count': str(basic_info.get('connection_count', '')),
                'linkedin_experience_summary': ' | '.join(experience_summary),
                'linkedin_education_summary': ' | '.join(education_summary),
                'linkedin_scraped_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'linkedin_enrichment_status': 'success'
            }
            
            return linkedin_data
            
        except Exception as e:
            logger.error(f"Error processing LinkedIn data: {e}")
            return {col: '' for col in self.linkedin_columns[:-1]} | {'linkedin_enrichment_status': 'processing_failed'}
    
    def enrich_with_linkedin(self, linkedin_url: str) -> Dict[str, str]:
        """Main method to enrich a lead with LinkedIn data"""
        # Initialize result with empty values
        result = {col: '' for col in self.linkedin_columns}
        result['linkedin_enrichment_status'] = 'failed'
        result['linkedin_scraped_at'] = time.strftime('%Y-%m-%d %H:%M:%S')
        
        # Extract username from LinkedIn URL
        username = self.extract_linkedin_username(linkedin_url)
        if not username:
            result['linkedin_enrichment_status'] = 'invalid_url'
            return result
        
        # Scrape LinkedIn profile
        profile_data = self.scrape_linkedin_profile(username)
        if not profile_data:
            result['linkedin_enrichment_status'] = 'scraping_failed'
            return result
        
        # Process the data
        processed_data = self.process_linkedin_data(profile_data)
        result.update(processed_data)
        
        self.success_count += 1
        return result
    
    def get_stats(self) -> Dict[str, int]:
        """Get enrichment statistics"""
        return {
            'processed': self.processed_count,
            'success': self.success_count,
            'errors': self.error_count
        }

# Test function
def test_linkedin_enrichment():
    """Test the LinkedIn enrichment functionality"""
    print("Testing LinkedIn enrichment...")
    
    enricher = LinkedInEnricher("**********************************************")
    
    # Test with a sample LinkedIn URL
    test_url = "https://www.linkedin.com/in/reidhoffman"
    
    print(f"Testing with URL: {test_url}")
    result = enricher.enrich_with_linkedin(test_url)
    
    print("\nLinkedIn Enrichment Results:")
    print("="*50)
    for key, value in result.items():
        print(f"{key}: {value}")
    
    print(f"\nStats: {enricher.get_stats()}")

if __name__ == "__main__":
    test_linkedin_enrichment()
