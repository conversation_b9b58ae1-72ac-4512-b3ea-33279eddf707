import React, { useState } from 'react';
import { importAPI } from '../services/api';

const CSVImport = ({ onClose, onImportComplete, defaultListId = 'default', lists = [] }) => {
  const [file, setFile] = useState(null);
  const [selectedListId, setSelectedListId] = useState(defaultListId);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [result, setResult] = useState(null);
  const [preview, setPreview] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const [analysis, setAnalysis] = useState(null);
  const [customMapping, setCustomMapping] = useState(null);
  const [step, setStep] = useState('upload'); // upload, analyze, map, preview, import

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      if (selectedFile.type === 'text/csv' || selectedFile.name.endsWith('.csv')) {
        setFile(selectedFile);
        setError(null);
        setPreview(null);
        setResult(null);
        setAnalysis(null);
        setCustomMapping(null);
        setShowPreview(false);
        setStep('upload');
      } else {
        setError('Please select a CSV file');
        setFile(null);
        setPreview(null);
        setAnalysis(null);
      }
    }
  };

  const handleAnalyze = async () => {
    if (!file) {
      setError('Please select a CSV file');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await importAPI.analyzeCSV(file);
      setAnalysis(response.data);
      // Initialize mapping with suggestions
      setCustomMapping(response.data.suggestedMapping || {});
      setStep('analyze');
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };



  const handlePreview = async () => {
    if (!file) {
      setError('Please select a CSV file');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await importAPI.previewCSV(file, customMapping);
      setPreview(response.data);
      setShowPreview(true);
      setStep('preview');
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!file) {
      setError('Please select a CSV file');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setResult(null);
      
      console.log('Uploading CSV with mapping:', customMapping);
      const response = await importAPI.uploadCSV(file, selectedListId, customMapping);
      console.log('Import response:', response.data);
      setResult(response.data);
      setStep('import');
      
      if (response.data.imported > 0) {
        // Auto-close after successful import
        setTimeout(() => {
          onImportComplete();
        }, 3000);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await importAPI.downloadTemplate();
      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'contacts_template.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      setError('Failed to download template');
    }
  };

  return (
    <div className="modal-overlay" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        width: '90%',
        maxWidth: '600px',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        <h3>Import Contacts from CSV</h3>
        
        {/* Step indicator */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginBottom: '2rem',
          padding: '1rem',
          backgroundColor: '#f8f9fa',
          borderRadius: '4px'
        }}>
          {[
            { key: 'upload', label: '1. Upload' },
            { key: 'analyze', label: '2. Analyze' },
            { key: 'map', label: '3. Map Columns' },
            { key: 'preview', label: '4. Preview' },
            { key: 'import', label: '5. Import' }
          ].map((stepInfo) => (
            <div
              key={stepInfo.key}
              style={{
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                backgroundColor:
                  step === stepInfo.key ? '#3498db' :
                  ['analyze', 'map', 'preview', 'import'].includes(step) &&
                  ['upload', 'analyze'].includes(stepInfo.key) ? '#27ae60' :
                  step === 'map' && stepInfo.key === 'analyze' ? '#27ae60' :
                  step === 'preview' && ['analyze', 'map'].includes(stepInfo.key) ? '#27ae60' :
                  step === 'import' && ['analyze', 'map', 'preview'].includes(stepInfo.key) ? '#27ae60' :
                  '#e9ecef',
                color:
                  step === stepInfo.key ? 'white' :
                  ['analyze', 'map', 'preview', 'import'].includes(step) &&
                  ['upload', 'analyze'].includes(stepInfo.key) ? 'white' :
                  step === 'map' && stepInfo.key === 'analyze' ? 'white' :
                  step === 'preview' && ['analyze', 'map'].includes(stepInfo.key) ? 'white' :
                  step === 'import' && ['analyze', 'map', 'preview'].includes(stepInfo.key) ? 'white' :
                  '#6c757d',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                textAlign: 'center'
              }}
            >
              {stepInfo.label}
            </div>
          ))}
        </div>

        <div style={{
          backgroundColor: '#e7f3ff',
          padding: '1rem',
          borderRadius: '4px',
          marginBottom: '1rem',
          fontSize: '0.9rem'
        }}>
          <strong>Smart CSV Import:</strong> Upload your CSV file and we'll help you map the columns to the right contact fields.
          <br />
          <small style={{ color: '#6c757d', marginTop: '0.5rem', display: 'block' }}>
            ✅ Supports both personal contacts (with names) and business contacts (with company names)
            <br />
            ✅ Automatically detects emails, phones, URLs, and social media links
            <br />
            ✅ Only email is required - all other fields are optional
          </small>
          <button
            type="button"
            onClick={handleDownloadTemplate}
            style={{
              background: 'none',
              border: 'none',
              color: '#3498db',
              textDecoration: 'underline',
              cursor: 'pointer',
              marginTop: '0.5rem'
            }}
          >
            Download template file
          </button>
        </div>

        {error && (
          <div style={{
            backgroundColor: '#f8d7da',
            color: '#721c24',
            padding: '0.75rem',
            borderRadius: '4px',
            marginBottom: '1rem'
          }}>
            {error}
          </div>
        )}

        {result && (
          <div style={{
            backgroundColor: result.errors > 0 ? '#fff3cd' : '#d4edda',
            color: result.errors > 0 ? '#856404' : '#155724',
            padding: '1rem',
            borderRadius: '4px',
            marginBottom: '1rem'
          }}>
            <div><strong>Import completed!</strong></div>
            <div>Successfully imported: {result.imported} contacts</div>
            {result.errors > 0 && (
              <div>Failed to import: {result.errors} contacts</div>
            )}
            {result.warnings > 0 && (
              <div>Warnings: {result.warnings}</div>
            )}

            {/* Column Detection Info */}
            {result.columnMapping && (
              <details style={{ marginTop: '1rem' }}>
                <summary style={{ cursor: 'pointer' }}>Column Detection Results</summary>
                <div style={{
                  marginTop: '0.5rem',
                  fontSize: '0.9rem',
                  backgroundColor: 'rgba(255,255,255,0.3)',
                  padding: '0.5rem',
                  borderRadius: '4px'
                }}>
                  <div><strong>Detected Headers:</strong> {result.detectedHeaders?.join(', ')}</div>
                  <div style={{ marginTop: '0.5rem' }}><strong>Column Mapping:</strong></div>
                  <ul style={{ margin: '0.5rem 0', paddingLeft: '1.5rem' }}>
                    {Object.entries(result.columnMapping).map(([field, column]) => (
                      <li key={field}>
                        <strong>{field}:</strong> {column || 'Not detected'}
                      </li>
                    ))}
                  </ul>
                </div>
              </details>
            )}

            {/* Warnings */}
            {result.details?.warnings?.length > 0 && (
              <details style={{ marginTop: '1rem' }}>
                <summary style={{ cursor: 'pointer' }}>View warnings ({result.warnings})</summary>
                <div style={{
                  marginTop: '0.5rem',
                  fontSize: '0.9rem'
                }}>
                  {result.details.warnings.map((warning, index) => (
                    <div key={index} style={{ marginBottom: '0.5rem' }}>
                      ⚠️ {warning}
                    </div>
                  ))}
                </div>
              </details>
            )}

            {/* Errors */}
            {result.details?.failed?.length > 0 && (
              <details style={{ marginTop: '1rem' }}>
                <summary style={{ cursor: 'pointer' }}>View errors ({result.errors})</summary>
                <div style={{
                  maxHeight: '200px',
                  overflow: 'auto',
                  marginTop: '0.5rem',
                  fontSize: '0.9rem'
                }}>
                  {result.details.failed.map((error, index) => (
                    <div key={index} style={{
                      marginBottom: '1rem',
                      padding: '0.5rem',
                      backgroundColor: 'rgba(255,255,255,0.3)',
                      borderRadius: '4px'
                    }}>
                      <div><strong>Line {error.line}:</strong> {error.error}</div>
                      {error.data && (
                        <div style={{ marginTop: '0.25rem' }}>
                          <small><strong>Processed:</strong> {JSON.stringify(error.data)}</small>
                        </div>
                      )}
                      {error.rawData && (
                        <details style={{ marginTop: '0.25rem' }}>
                          <summary style={{ cursor: 'pointer', fontSize: '0.8rem' }}>Raw CSV data</summary>
                          <small>{JSON.stringify(error.rawData)}</small>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              </details>
            )}

            {/* Success Preview */}
            {result.details?.successful?.length > 0 && (
              <details style={{ marginTop: '1rem' }}>
                <summary style={{ cursor: 'pointer' }}>Preview imported contacts ({result.imported})</summary>
                <div style={{
                  maxHeight: '200px',
                  overflow: 'auto',
                  marginTop: '0.5rem',
                  fontSize: '0.9rem'
                }}>
                  {result.details.successful.slice(0, 5).map((contact, index) => (
                    <div key={index} style={{
                      marginBottom: '0.5rem',
                      padding: '0.5rem',
                      backgroundColor: 'rgba(255,255,255,0.3)',
                      borderRadius: '4px'
                    }}>
                      <div><strong>{contact.name}</strong> - {contact.email}</div>
                      {contact.phone && <div>Phone: {contact.phone}</div>}
                      <div>Status: {contact.status}</div>
                    </div>
                  ))}
                  {result.details.successful.length > 5 && (
                    <div style={{ textAlign: 'center', fontStyle: 'italic' }}>
                      ... and {result.details.successful.length - 5} more contacts
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="listId">Import to List</label>
            <select
              id="listId"
              value={selectedListId}
              onChange={(e) => setSelectedListId(e.target.value)}
              className="form-control"
              disabled={loading}
            >
              {lists.map((list) => (
                <option key={list.id} value={list.id}>
                  {list.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="csvFile">Select CSV File</label>
            <input
              type="file"
              id="csvFile"
              accept=".csv,text/csv"
              onChange={handleFileChange}
              className="form-control"
              disabled={loading}
            />
            {file && (
              <div style={{ marginTop: '0.5rem', fontSize: '0.9rem', color: '#6c757d' }}>
                Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
              </div>
            )}
          </div>

          {/* CSV Preview and Column Mapping */}
          {analysis && step === 'analyze' && (
            <div style={{
              backgroundColor: '#f8f9fa',
              padding: '1.5rem',
              borderRadius: '8px',
              marginTop: '1rem',
              border: '1px solid #dee2e6'
            }}>
              <h4 style={{ marginBottom: '1rem' }}>Your CSV Data Preview</h4>

              <div style={{ marginBottom: '1.5rem', fontSize: '0.9rem', color: '#6c757d' }}>
                <strong>File:</strong> {file.name} • <strong>Rows:</strong> {analysis.totalRows} • <strong>Columns:</strong> {analysis.detectedHeaders.length}
              </div>

              {/* CSV Data Table */}
              <div style={{
                overflowX: 'auto',
                marginBottom: '2rem',
                border: '1px solid #dee2e6',
                borderRadius: '4px'
              }}>
                <table style={{
                  width: '100%',
                  borderCollapse: 'collapse',
                  fontSize: '0.85rem'
                }}>
                  <thead>
                    <tr style={{ backgroundColor: '#e9ecef' }}>
                      {analysis.detectedHeaders.map((header, index) => (
                        <th key={index} style={{
                          padding: '0.75rem 0.5rem',
                          textAlign: 'left',
                          borderRight: '1px solid #dee2e6',
                          fontWeight: 'bold',
                          minWidth: '120px'
                        }}>
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {analysis.sampleData.map((row, rowIndex) => (
                      <tr key={rowIndex} style={{
                        borderBottom: '1px solid #dee2e6',
                        backgroundColor: rowIndex % 2 === 0 ? 'white' : '#f8f9fa'
                      }}>
                        {analysis.detectedHeaders.map((header, colIndex) => (
                          <td key={colIndex} style={{
                            padding: '0.5rem',
                            borderRight: '1px solid #dee2e6',
                            maxWidth: '150px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {row[header] || '-'}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Column Mapping Interface */}
              <h4 style={{ marginBottom: '1rem' }}>Map Your Columns</h4>
              <p style={{ marginBottom: '1.5rem', color: '#6c757d', fontSize: '0.9rem' }}>
                Choose which contact field each of your CSV columns should map to:
              </p>

              <div style={{
                display: 'grid',
                gap: '1rem',
                marginBottom: '2rem'
              }}>
                {analysis.detectedHeaders.map((header, index) => (
                  <div key={index} style={{
                    display: 'grid',
                    gridTemplateColumns: '1fr 2fr 1fr',
                    gap: '1rem',
                    alignItems: 'center',
                    padding: '1rem',
                    backgroundColor: 'white',
                    borderRadius: '4px',
                    border: '1px solid #dee2e6'
                  }}>
                    {/* CSV Column */}
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
                        {header}
                      </div>
                      <div style={{ fontSize: '0.8rem', color: '#6c757d', marginBottom: '0.25rem' }}>
                        Sample: "{analysis.sampleData[0]?.[header] || 'No data'}"
                      </div>
                      {/* Pattern indicators */}
                      <div style={{ fontSize: '0.7rem', display: 'flex', gap: '0.25rem', flexWrap: 'wrap' }}>
                        {(() => {
                          const colAnalysis = analysis.columnAnalysis.find(c => c.header === header);
                          const indicators = [];
                          if (colAnalysis?.hasEmailPattern) indicators.push('📧 Email');
                          if (colAnalysis?.hasPhonePattern) indicators.push('📞 Phone');
                          if (colAnalysis?.hasCompanyPattern) indicators.push('🏢 Company');
                          if (colAnalysis?.hasLinkedInPattern) indicators.push('💼 LinkedIn');
                          else if (colAnalysis?.hasFacebookPattern) indicators.push('📘 Facebook');
                          else if (colAnalysis?.hasInstagramPattern) indicators.push('📷 Instagram');
                          else if (colAnalysis?.hasUrlPattern) indicators.push('🌐 URL');
                          if (colAnalysis?.hasSocialPattern && !colAnalysis?.hasLinkedInPattern && !colAnalysis?.hasFacebookPattern && !colAnalysis?.hasInstagramPattern) indicators.push('📱 Social');
                          if (colAnalysis?.hasNamePattern && !colAnalysis?.hasCompanyPattern) indicators.push('👤 Name');
                          return indicators.map((indicator, i) => (
                            <span key={i} style={{
                              backgroundColor: '#e9ecef',
                              padding: '0.1rem 0.3rem',
                              borderRadius: '3px',
                              color: '#495057'
                            }}>
                              {indicator}
                            </span>
                          ));
                        })()}
                      </div>
                    </div>

                    {/* Mapping Dropdown */}
                    <div>
                      <select
                        value={customMapping[header] || ''}
                        onChange={(e) => {
                          const newMapping = { ...customMapping };

                          // Remove this field from other columns
                          Object.keys(newMapping).forEach(key => {
                            if (newMapping[key] === e.target.value && key !== header) {
                              newMapping[key] = '';
                            }
                          });

                          newMapping[header] = e.target.value;
                          setCustomMapping(newMapping);
                        }}
                        style={{
                          width: '100%',
                          padding: '0.5rem',
                          border: '1px solid #ddd',
                          borderRadius: '4px',
                          fontSize: '0.9rem'
                        }}
                      >
                        <option value="">-- Do not import --</option>
                        <optgroup label="Basic Information">
                          <option value="firstName">First Name</option>
                          <option value="lastName">Last Name</option>
                          <option value="name">Full Name</option>
                          <option value="email">Email Address</option>
                          <option value="phone">Phone Number</option>
                          <option value="status">Status</option>
                        </optgroup>
                        <optgroup label="Company Information">
                          <option value="company">Company</option>
                          <option value="companyType">Company Type</option>
                          <option value="title">Job Title</option>
                        </optgroup>
                        <optgroup label="URLs & Social Media">
                          <option value="website">Website URL</option>
                          <option value="linkedinUrl">LinkedIn URL</option>
                          <option value="instagramUrl">Instagram URL</option>
                          <option value="facebookUrl">Facebook URL</option>
                        </optgroup>
                        <optgroup label="Address">
                          <option value="address">Address</option>
                          <option value="city">City</option>
                          <option value="state">State/Province</option>
                          <option value="zip">ZIP/Postal Code</option>
                          <option value="country">Country</option>
                        </optgroup>
                        <optgroup label="Additional">
                          <option value="notes">Notes</option>
                          <option value="CUSTOM_FIELD">📋 Save as Custom Field</option>
                        </optgroup>
                      </select>
                    </div>

                    {/* Confidence Indicator */}
                    <div style={{ textAlign: 'center' }}>
                      {customMapping[header] && (
                        <span style={{
                          padding: '0.25rem 0.5rem',
                          borderRadius: '4px',
                          fontSize: '0.8rem',
                          fontWeight: 'bold',
                          backgroundColor:
                            customMapping[header] === 'CUSTOM_FIELD' ? '#e7f3ff' :
                            analysis.suggestedMapping[header] === customMapping[header] ? '#d4edda' : '#fff3cd',
                          color:
                            customMapping[header] === 'CUSTOM_FIELD' ? '#0066cc' :
                            analysis.suggestedMapping[header] === customMapping[header] ? '#155724' : '#856404'
                        }}>
                          {customMapping[header] === 'CUSTOM_FIELD' ? 'CUSTOM' :
                           analysis.suggestedMapping[header] === customMapping[header] ? 'AUTO' : 'MANUAL'}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Action Buttons */}
              {/* Custom Fields Preview */}
              {Object.values(customMapping).filter(v => v === 'CUSTOM_FIELD').length > 0 && (
                <div style={{
                  padding: '1rem',
                  backgroundColor: '#e7f3ff',
                  borderRadius: '4px',
                  marginBottom: '1rem',
                  border: '1px solid #b3d9ff'
                }}>
                  <h5 style={{ marginBottom: '0.5rem', color: '#0066cc' }}>
                    📋 Custom Fields ({Object.values(customMapping).filter(v => v === 'CUSTOM_FIELD').length})
                  </h5>
                  <div style={{ fontSize: '0.9rem', color: '#495057' }}>
                    These columns will be saved as additional contact information:
                    <div style={{ marginTop: '0.5rem', display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
                      {Object.entries(customMapping)
                        .filter(([, value]) => value === 'CUSTOM_FIELD')
                        .map(([column]) => (
                          <span key={column} style={{
                            padding: '0.25rem 0.5rem',
                            backgroundColor: '#ffffff',
                            border: '1px solid #b3d9ff',
                            borderRadius: '4px',
                            fontSize: '0.8rem'
                          }}>
                            {column}
                          </span>
                        ))}
                    </div>
                  </div>
                </div>
              )}

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
                <button
                  onClick={() => setStep('map')}
                  className="btn btn-primary"
                  disabled={!Object.values(customMapping).some(v => v)}
                  style={{ minWidth: '150px' }}
                >
                  Continue with Mapping
                </button>
                <button
                  onClick={() => {
                    setCustomMapping(analysis.suggestedMapping);
                  }}
                  className="btn"
                  style={{ backgroundColor: '#28a745', color: 'white', minWidth: '150px' }}
                >
                  Use Auto-Suggestions
                </button>
              </div>
            </div>
          )}

          {/* Mapping Summary */}
          {customMapping && step === 'map' && (
            <div style={{
              backgroundColor: '#d4edda',
              padding: '1rem',
              borderRadius: '4px',
              marginTop: '1rem',
              border: '1px solid #c3e6cb'
            }}>
              <h4>✅ Column Mapping Ready</h4>
              <div style={{ fontSize: '0.9rem', marginBottom: '1rem' }}>
                {Object.entries(customMapping).filter(([, field]) => field).length} columns mapped for import
              </div>
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button
                  onClick={() => setStep('analyze')}
                  style={{
                    background: 'none',
                    border: '1px solid #155724',
                    color: '#155724',
                    padding: '0.5rem 1rem',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  ← Modify Mapping
                </button>
                <button
                  onClick={handlePreview}
                  className="btn btn-primary"
                  disabled={loading}
                >
                  {loading ? 'Generating Preview...' : 'Preview Import'}
                </button>
              </div>
            </div>
          )}

          {/* Preview Section */}
          {preview && showPreview && (
            <div style={{
              backgroundColor: '#f8f9fa',
              padding: '1rem',
              borderRadius: '4px',
              marginTop: '1rem',
              border: '1px solid #dee2e6'
            }}>
              <h4>CSV Preview</h4>

              {/* Column Detection */}
              <div style={{ marginBottom: '1rem' }}>
                <strong>Detected Columns:</strong>
                <div style={{ fontSize: '0.9rem', marginTop: '0.5rem' }}>
                  {Object.entries(preview.columnMapping).map(([field, column]) => (
                    <span key={field} style={{
                      display: 'inline-block',
                      margin: '0.25rem 0.5rem 0.25rem 0',
                      padding: '0.25rem 0.5rem',
                      backgroundColor: column ? '#d4edda' : '#f8d7da',
                      color: column ? '#155724' : '#721c24',
                      borderRadius: '4px',
                      fontSize: '0.8rem'
                    }}>
                      {field}: {column || 'Not found'}
                    </span>
                  ))}
                </div>
              </div>

              {/* Data Preview */}
              <div style={{ marginBottom: '1rem' }}>
                <strong>Data Preview ({preview.preview.length} of {preview.totalRows} rows):</strong>
                <div style={{
                  maxHeight: '300px',
                  overflow: 'auto',
                  marginTop: '0.5rem',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px'
                }}>
                  <table style={{ width: '100%', fontSize: '0.8rem' }}>
                    <thead style={{ backgroundColor: '#e9ecef', position: 'sticky', top: 0 }}>
                      <tr>
                        <th style={{ padding: '0.5rem', textAlign: 'left' }}>Line</th>
                        <th style={{ padding: '0.5rem', textAlign: 'left' }}>Name</th>
                        <th style={{ padding: '0.5rem', textAlign: 'left' }}>Email</th>
                        <th style={{ padding: '0.5rem', textAlign: 'left' }}>Phone</th>
                        <th style={{ padding: '0.5rem', textAlign: 'left' }}>Status</th>
                        <th style={{ padding: '0.5rem', textAlign: 'center' }}>Valid</th>
                      </tr>
                    </thead>
                    <tbody>
                      {preview.preview.map((row, index) => (
                        <tr key={index} style={{
                          backgroundColor: row.valid ? 'transparent' : '#fff3cd',
                          borderBottom: '1px solid #dee2e6'
                        }}>
                          <td style={{ padding: '0.5rem' }}>{row.line}</td>
                          <td style={{ padding: '0.5rem' }}>{row.processed.name || '-'}</td>
                          <td style={{ padding: '0.5rem' }}>{row.processed.email || '-'}</td>
                          <td style={{ padding: '0.5rem' }}>{row.processed.phone || '-'}</td>
                          <td style={{ padding: '0.5rem' }}>{row.processed.status || '-'}</td>
                          <td style={{ padding: '0.5rem', textAlign: 'center' }}>
                            {row.valid ? '✅' : '❌'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div style={{ fontSize: '0.9rem', color: '#6c757d' }}>
                💡 Yellow rows indicate potential issues that may prevent import.
              </div>
            </div>
          )}

          <div style={{ display: 'flex', gap: '1rem', marginTop: '2rem' }}>
            {/* Step-based action buttons */}
            {step === 'upload' && file && (
              <button
                type="button"
                className="btn btn-primary"
                onClick={handleAnalyze}
                disabled={loading}
                style={{ flex: 1 }}
              >
                {loading ? 'Analyzing CSV...' : 'Analyze & Map Columns'}
              </button>
            )}

            {step === 'preview' && (
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading || !file || !customMapping}
                style={{ flex: 1 }}
              >
                {loading ? 'Importing...' : 'Import Contacts'}
              </button>
            )}

            <button
              type="button"
              className="btn"
              onClick={onClose}
              disabled={loading}
              style={{
                backgroundColor: '#6c757d',
                color: 'white'
              }}
            >
              {result?.imported > 0 ? 'Close' : 'Cancel'}
            </button>
          </div>
        </form>

        {result?.imported > 0 && (
          <div style={{
            textAlign: 'center',
            marginTop: '1rem',
            fontSize: '0.9rem',
            color: '#6c757d'
          }}>
            This dialog will close automatically in a few seconds...
          </div>
        )}


      </div>
    </div>
  );
};

export default CSVImport;
