import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { contactAPI, conversationAPI } from '../services/api';
import ConversationForm from './ConversationForm';
import ContactForm from './ContactForm';

const ContactDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [contact, setContact] = useState(null);
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showConversationForm, setShowConversationForm] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);

  useEffect(() => {
    loadContactData();
  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadContactData = async () => {
    try {
      setLoading(true);
      const [contactResponse, conversationsResponse] = await Promise.all([
        contactAPI.getById(id),
        conversationAPI.getByContactId(id)
      ]);
      
      setContact(contactResponse.data);
      setConversations(conversationsResponse.data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleConversationCreated = () => {
    setShowConversationForm(false);
    loadContactData();
  };

  const handleContactUpdated = () => {
    setShowContactForm(false);
    loadContactData();
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  const groupConversationsByDate = (conversations) => {
    const groups = {};
    conversations.forEach(conv => {
      const date = formatDate(conv.timestamp);
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(conv);
    });
    return groups;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem' }}>
        Loading contact details...
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
        Error: {error}
        <br />
        <button className="btn btn-primary" onClick={loadContactData} style={{ marginTop: '1rem' }}>
          Retry
        </button>
        <button className="btn" onClick={() => navigate('/')} style={{ marginTop: '1rem', marginLeft: '1rem' }}>
          Back to Dashboard
        </button>
      </div>
    );
  }

  if (!contact) {
    return (
      <div style={{ textAlign: 'center', padding: '2rem' }}>
        Contact not found
        <br />
        <button className="btn btn-primary" onClick={() => navigate('/')} style={{ marginTop: '1rem' }}>
          Back to Dashboard
        </button>
      </div>
    );
  }

  const conversationGroups = groupConversationsByDate(conversations);

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: window.innerWidth > 768 ? 'center' : 'flex-start',
        flexDirection: window.innerWidth > 768 ? 'row' : 'column',
        gap: window.innerWidth > 768 ? '0' : '1rem',
        marginBottom: '2rem',
        padding: '1.5rem',
        backgroundColor: 'white',
        borderRadius: '12px',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <button
            className="btn"
            onClick={() => navigate('/')}
            style={{
              marginRight: '1.5rem',
              backgroundColor: '#6c757d',
              color: 'white',
              borderRadius: '8px',
              padding: '0.5rem 1rem'
            }}
          >
            ← Back to Dashboard
          </button>
          <div>
            <h1 style={{ fontSize: '1.8rem', fontWeight: 'bold', margin: 0, color: '#2c3e50' }}>
              {contact.name}
            </h1>
            {contact.title && contact.company && (
              <p style={{ margin: '0.25rem 0 0 0', color: '#6c757d', fontSize: '1rem' }}>
                {contact.title} at {contact.company}
              </p>
            )}
          </div>
        </div>
        <div>
          <button
            className="btn btn-primary"
            onClick={() => setShowContactForm(true)}
            style={{
              borderRadius: '8px',
              padding: '0.75rem 1.5rem'
            }}
          >
            ✏️ Edit Contact
          </button>
        </div>
      </div>

      {/* Main Content - Responsive Two Column Layout */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: window.innerWidth > 768 ? '1fr 1fr' : '1fr',
        gap: '2rem',
        marginBottom: '2rem'
      }}>

        {/* Left Column - Contact Info */}
        <div style={{
          padding: '1.5rem',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
          height: 'fit-content'
        }}>
        {/* Basic Information */}
        <div style={{ marginBottom: '1.5rem' }}>
          <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Contact Information</h4>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
            <div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Email:</strong>
                <a href={`mailto:${contact.email}`} style={{ marginLeft: '0.5rem', color: '#3498db' }}>
                  {contact.email}
                </a>
              </div>
              {contact.phone && (
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Phone:</strong>
                  <a href={`tel:${contact.phone}`} style={{ marginLeft: '0.5rem', color: '#3498db' }}>
                    {contact.phone}
                  </a>
                </div>
              )}
            </div>
            <div>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>Status:</strong>
                <span className={`contact-status ${contact.status}`} style={{ marginLeft: '0.5rem' }}>
                  {contact.status}
                </span>
              </div>
              <div style={{ fontSize: '0.9rem', color: '#6c757d' }}>
                <div>Conversations: {contact.conversationCount || 0}</div>
                <div>Last contact: {contact.lastContactDate ? formatTimestamp(contact.lastContactDate) : 'Never'}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Company Information */}
        {(contact.company || contact.title || contact.companyType) && (
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Company Information</h4>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
              {contact.company && (
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Company:</strong> {contact.company}
                </div>
              )}
              {contact.title && (
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Title:</strong> {contact.title}
                </div>
              )}
              {contact.companyType && (
                <div style={{ marginBottom: '0.5rem' }}>
                  <strong>Company Type:</strong> {contact.companyType}
                </div>
              )}
            </div>
          </div>
        )}

        {/* URLs and Social Media */}
        {(contact.website || contact.linkedinUrl || contact.instagramUrl || contact.facebookUrl) && (
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Online Presence</h4>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '1rem' }}>
              {contact.website && (
                <a
                  href={contact.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#3498db',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}
                >
                  🌐 Website
                </a>
              )}
              {contact.linkedinUrl && (
                <a
                  href={contact.linkedinUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#0077b5',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}
                >
                  💼 LinkedIn
                </a>
              )}
              {contact.instagramUrl && (
                <a
                  href={contact.instagramUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#e4405f',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}
                >
                  📷 Instagram
                </a>
              )}
              {contact.facebookUrl && (
                <a
                  href={contact.facebookUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#1877f2',
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '4px',
                    fontSize: '0.9rem'
                  }}
                >
                  📘 Facebook
                </a>
              )}
            </div>
          </div>
        )}

        {/* Address Information */}
        {(contact.address || contact.city || contact.state || contact.zip || contact.country) && (
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Address</h4>
            <div>
              {contact.address && <div>{contact.address}</div>}
              <div>
                {[contact.city, contact.state, contact.zip].filter(Boolean).join(', ')}
                {contact.country && <div>{contact.country}</div>}
              </div>
            </div>
          </div>
        )}

        {/* Custom Fields - Enhanced with Collapsible Sections */}
        {contact.customFields && Object.keys(contact.customFields).length > 0 && (
          <AdditionalDataSection customFields={contact.customFields} />
        )}

        {/* Notes */}
        {contact.notes && (
          <div>
            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Notes</h4>
            <div style={{
              padding: '1rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px',
              fontSize: '0.9rem',
              lineHeight: '1.5'
            }}>
              {contact.notes}
            </div>
          </div>
        )}
        </div>

        {/* Right Column - Conversations */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
          minHeight: '600px',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <div style={{
            padding: '1.5rem',
            borderBottom: '1px solid #eee',
            fontWeight: 'bold',
            fontSize: '1.2rem',
            color: '#2c3e50',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>💬 Conversations ({conversations.length})</span>
            <button
              className="btn btn-sm btn-success"
              onClick={() => setShowConversationForm(true)}
              style={{
                borderRadius: '6px',
                padding: '0.5rem 1rem',
                fontSize: '0.9rem'
              }}
            >
              + Add
            </button>
          </div>

          <div style={{
            padding: '1.5rem',
            flex: 1,
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {conversations.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '3rem 2rem',
                color: '#6c757d',
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center'
              }}>
                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>💬</div>
                <div style={{ fontSize: '1.1rem', marginBottom: '0.5rem' }}>No conversations yet</div>
                <div style={{ fontSize: '0.9rem' }}>Start a conversation to track your communications</div>
              </div>
            ) : (
            Object.entries(conversationGroups).map(([date, dayConversations]) => (
              <div key={date} style={{ marginBottom: '2rem' }}>
                <div style={{ 
                  textAlign: 'center',
                  margin: '1rem 0',
                  padding: '0.5rem',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                  fontSize: '0.9rem',
                  color: '#6c757d'
                }}>
                  {date}
                </div>
                
                {dayConversations.map((conversation) => (
                  <div 
                    key={conversation.id}
                    style={{
                      display: 'flex',
                      justifyContent: conversation.direction === 'sent' ? 'flex-end' : 'flex-start',
                      marginBottom: '1rem'
                    }}
                  >
                    <div style={{
                      maxWidth: '70%',
                      padding: '1rem',
                      borderRadius: '12px',
                      backgroundColor: conversation.direction === 'sent' ? '#3498db' : '#e9ecef',
                      color: conversation.direction === 'sent' ? 'white' : '#2c3e50'
                    }}>
                      {conversation.subject && (
                        <div style={{ 
                          fontWeight: 'bold', 
                          marginBottom: '0.5rem',
                          fontSize: '0.9rem'
                        }}>
                          {conversation.subject}
                        </div>
                      )}
                      <div style={{ marginBottom: '0.5rem' }}>
                        {conversation.content}
                      </div>
                      <div style={{ 
                        fontSize: '0.8rem',
                        opacity: 0.8,
                        textAlign: 'right'
                      }}>
                        {new Date(conversation.timestamp).toLocaleTimeString()}
                        {conversation.status !== 'read' && (
                          <span style={{ marginLeft: '0.5rem' }}>
                            • {conversation.status}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ))
            )}
          </div>
        </div>
      </div>

      {showConversationForm && (
        <ConversationForm 
          contactId={id}
          onClose={() => setShowConversationForm(false)}
          onConversationCreated={handleConversationCreated}
        />
      )}

      {showContactForm && (
        <ContactForm 
          contact={contact}
          onClose={() => setShowContactForm(false)}
          onContactCreated={handleContactUpdated}
        />
      )}
    </div>
  );
};

// Additional Data Section Component with Collapsible Categories
const AdditionalDataSection = ({ customFields }) => {
  const [expandedSections, setExpandedSections] = useState({});

  const toggleSection = (sectionName) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  };

  // Categorize the custom fields
  const categorizeFields = (fields) => {
    const categories = {
      'Professional Info': [],
      'Company Details': [],
      'Organization Info': [],
      'AI Insights': [],
      'Other': []
    };

    Object.entries(fields).forEach(([fieldName, value]) => {
      const lowerFieldName = fieldName.toLowerCase();

      if (lowerFieldName.includes('headline') || lowerFieldName.includes('seniority')) {
        categories['Professional Info'].push([fieldName, value]);
      } else if (lowerFieldName.includes('organization') && !lowerFieldName.includes('ai')) {
        categories['Organization Info'].push([fieldName, value]);
      } else if (lowerFieldName.includes('ai ') || lowerFieldName.startsWith('ai')) {
        categories['AI Insights'].push([fieldName, value]);
      } else if (lowerFieldName.includes('estimated') || lowerFieldName.includes('founded') ||
                 lowerFieldName.includes('revenue') || lowerFieldName.includes('keywords')) {
        categories['Company Details'].push([fieldName, value]);
      } else {
        categories['Other'].push([fieldName, value]);
      }
    });

    // Remove empty categories
    return Object.fromEntries(
      Object.entries(categories).filter(([_, fields]) => fields.length > 0)
    );
  };

  const categorizedFields = categorizeFields(customFields);

  const renderFieldValue = (value) => {
    if (typeof value === 'string' && value.length > 200) {
      return (
        <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>
          <div style={{
            maxHeight: '100px',
            overflowY: 'auto',
            padding: '0.5rem',
            backgroundColor: '#ffffff',
            border: '1px solid #e9ecef',
            borderRadius: '4px'
          }}>
            {value}
          </div>
        </div>
      );
    }
    return (
      <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>
        {value}
      </div>
    );
  };

  return (
    <div style={{ marginBottom: '1.5rem' }}>
      <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Additional Information</h4>

      {Object.entries(categorizedFields).map(([categoryName, fields]) => (
        <div key={categoryName} style={{ marginBottom: '1rem' }}>
          <button
            onClick={() => toggleSection(categoryName)}
            style={{
              width: '100%',
              padding: '0.75rem',
              backgroundColor: '#f8f9fa',
              border: '1px solid #dee2e6',
              borderRadius: '6px',
              cursor: 'pointer',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              fontSize: '1rem',
              fontWeight: 'bold',
              color: '#495057',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#e9ecef';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#f8f9fa';
            }}
          >
            <span>{categoryName} ({fields.length})</span>
            <span style={{
              transform: expandedSections[categoryName] ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.2s'
            }}>
              ▼
            </span>
          </button>

          {expandedSections[categoryName] && (
            <div style={{
              marginTop: '0.5rem',
              padding: '1rem',
              backgroundColor: '#ffffff',
              border: '1px solid #dee2e6',
              borderTop: 'none',
              borderRadius: '0 0 6px 6px',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              gap: '1rem'
            }}>
              {fields.map(([fieldName, value]) => (
                <div key={fieldName} style={{
                  padding: '0.75rem',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                  border: '1px solid #e9ecef'
                }}>
                  <div style={{
                    fontWeight: 'bold',
                    fontSize: '0.9rem',
                    marginBottom: '0.5rem',
                    color: '#495057'
                  }}>
                    {fieldName}
                  </div>
                  {renderFieldValue(value)}
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ContactDetail;
