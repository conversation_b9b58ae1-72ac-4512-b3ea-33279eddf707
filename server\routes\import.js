const express = require('express');
const multer = require('multer');
const csv = require('csv-parser');
const fs = require('fs');
const router = express.Router();
const Contact = require('../models/Contact');
const { readJsonFile, writeJsonFile, CONTACTS_FILE, LISTS_FILE } = require('../utils/dataUtils');

// Configure multer for file uploads
const upload = multer({ 
  dest: 'uploads/',
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('Only CSV files are allowed'), false);
    }
  }
});

// Helper function to detect and map CSV columns
const detectColumnMapping = (headers) => {
  const mapping = {};

  // Common variations for each field
  const fieldVariations = {
    firstName: ['first_name', 'firstname', 'fname', 'given_name', 'givenname', 'first', 'prenom', 'contact_first_name'],
    lastName: ['last_name', 'lastname', 'lname', 'family_name', 'familyname', 'last', 'surname', 'nom', 'contact_last_name'],
    name: ['name', 'full_name', 'fullname', 'contact_name', 'contactname', 'display_name', 'person', 'contact', 'nom_complet', 'lead_name', 'prospect_name'],
    email: ['email', 'email_address', 'emailaddress', 'e_mail', 'mail', 'email_addr', 'contact_email', 'courriel', 'e-mail', 'business_email', 'work_email'],
    phone: ['phone', 'phone_number', 'phonenumber', 'telephone', 'tel', 'mobile', 'cell', 'contact_phone', 'phone_no', 'cellphone', 'mobile_phone', 'business_phone', 'work_phone', 'office_phone'],
    status: ['status', 'contact_status', 'state', 'active', 'type', 'category', 'statut', 'lead_status', 'prospect_status'],
    company: ['company', 'organization', 'org', 'business', 'employer', 'work', 'entreprise', 'corporation', 'company_name', 'business_name', 'organization_name', 'firm', 'agency', 'client', 'account'],
    companyType: ['company_type', 'companytype', 'business_type', 'org_type', 'organization_type', 'industry', 'sector', 'vertical', 'market'],
    title: ['title', 'job_title', 'position', 'role', 'designation', 'titre', 'job_position', 'job_role', 'function', 'department'],
    website: ['website', 'web', 'url', 'site', 'homepage', 'web_url', 'site_web', 'company_website', 'business_website', 'domain', 'link', 'web_site'],
    linkedinUrl: ['linkedin', 'linkedin_url', 'linkedin_profile', 'linkedin_link', 'li_url', 'linkedin_page', 'linkedin_handle', 'li', 'linked_in'],
    instagramUrl: ['instagram', 'instagram_url', 'instagram_profile', 'instagram_link', 'ig_url', 'insta', 'instagram_handle', 'ig', 'gram'],
    facebookUrl: ['facebook', 'facebook_url', 'facebook_profile', 'facebook_link', 'fb_url', 'fb', 'facebook_page', 'face_book'],
    address: ['address', 'street', 'location', 'addr', 'adresse', 'street_address', 'business_address', 'office_address', 'mailing_address'],
    city: ['city', 'town', 'ville', 'locality', 'municipality'],
    state: ['state', 'province', 'region', 'etat', 'province', 'state_province', 'territory'],
    zip: ['zip', 'postal_code', 'zipcode', 'postcode', 'postal', 'code_postal', 'zip_code', 'post_code'],
    country: ['country', 'nation', 'pays', 'country_code', 'nationality'],
    notes: ['notes', 'comments', 'description', 'memo', 'remarks', 'commentaires', 'additional_info', 'details', 'bio', 'about']
  };

  // Normalize headers for comparison
  const normalizedHeaders = headers.map(h => h.toLowerCase().trim().replace(/[^a-z0-9]/g, '_'));

  // Find best match for each field
  Object.keys(fieldVariations).forEach(field => {
    const variations = fieldVariations[field];

    // Try exact matches first
    for (let i = 0; i < normalizedHeaders.length; i++) {
      if (variations.includes(normalizedHeaders[i])) {
        mapping[field] = headers[i];
        break;
      }
    }

    // If no exact match, try partial matches
    if (!mapping[field]) {
      for (let i = 0; i < normalizedHeaders.length; i++) {
        for (const variation of variations) {
          if (normalizedHeaders[i].includes(variation) || variation.includes(normalizedHeaders[i])) {
            mapping[field] = headers[i];
            break;
          }
        }
        if (mapping[field]) break;
      }
    }
  });

  return mapping;
};

// Helper function to clean and validate data
const cleanContactData = (rawData, mapping, allHeaders) => {
  const cleaned = {};

  // Ensure mapping exists
  if (!mapping) {
    console.error('No mapping provided to cleanContactData');
    return cleaned;
  }

  // Extract basic data using mapping
  cleaned.firstName = mapping.firstName ? (rawData[mapping.firstName] || '') : '';
  cleaned.lastName = mapping.lastName ? (rawData[mapping.lastName] || '') : '';
  cleaned.name = mapping.name ? (rawData[mapping.name] || '') : '';
  cleaned.email = mapping.email ? (rawData[mapping.email] || '') : '';
  cleaned.phone = mapping.phone ? (rawData[mapping.phone] || '') : '';
  cleaned.status = mapping.status ? (rawData[mapping.status] || 'active') : 'active';

  // Company information
  cleaned.company = mapping.company ? (rawData[mapping.company] || '') : '';
  cleaned.companyType = mapping.companyType ? (rawData[mapping.companyType] || '') : '';
  cleaned.title = mapping.title ? (rawData[mapping.title] || '') : '';

  // URLs and social media
  cleaned.website = mapping.website ? (rawData[mapping.website] || '') : '';
  cleaned.linkedinUrl = mapping.linkedinUrl ? (rawData[mapping.linkedinUrl] || '') : '';
  cleaned.instagramUrl = mapping.instagramUrl ? (rawData[mapping.instagramUrl] || '') : '';
  cleaned.facebookUrl = mapping.facebookUrl ? (rawData[mapping.facebookUrl] || '') : '';

  // Address information
  cleaned.address = mapping.address ? (rawData[mapping.address] || '') : '';
  cleaned.city = mapping.city ? (rawData[mapping.city] || '') : '';
  cleaned.state = mapping.state ? (rawData[mapping.state] || '') : '';
  cleaned.zip = mapping.zip ? (rawData[mapping.zip] || '') : '';
  cleaned.country = mapping.country ? (rawData[mapping.country] || '') : '';

  // Additional fields
  cleaned.notes = mapping.notes ? (rawData[mapping.notes] || '') : '';

  // Generate name from first/last name if name is empty
  if (!cleaned.name.trim() && (cleaned.firstName.trim() || cleaned.lastName.trim())) {
    cleaned.name = `${cleaned.firstName} ${cleaned.lastName}`.trim();
  }

  // Clean and validate email
  if (cleaned.email) {
    cleaned.email = cleaned.email.trim().toLowerCase();
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(cleaned.email)) {
      cleaned.email = ''; // Invalid email
    }
  }

  // Clean phone number
  if (cleaned.phone) {
    // Remove common phone formatting but keep basic structure
    cleaned.phone = cleaned.phone.replace(/[^\d+\-\(\)\s\.]/g, '').trim();
  }

  // Normalize status
  if (cleaned.status) {
    const statusLower = cleaned.status.toLowerCase().trim();
    if (['active', 'pending', 'archived', 'inactive'].includes(statusLower)) {
      cleaned.status = statusLower === 'inactive' ? 'archived' : statusLower;
    } else {
      cleaned.status = 'active'; // Default status
    }
  }

  // Clean URLs
  const urlFields = ['website', 'linkedinUrl', 'instagramUrl', 'facebookUrl'];
  urlFields.forEach(field => {
    if (cleaned[field] && cleaned[field].trim()) {
      let url = cleaned[field].trim();
      // Add https:// if no protocol specified and it looks like a URL
      if (!url.startsWith('http://') && !url.startsWith('https://') &&
          (url.includes('.') || url.startsWith('www.'))) {
        url = 'https://' + url;
      }
      cleaned[field] = url;
    }
  });

  // Handle custom fields - any unmapped columns with data
  cleaned.customFields = {};
  if (allHeaders) {
    const mappedColumns = new Set(Object.values(mapping).filter(v => v));

    allHeaders.forEach(header => {
      // If this column isn't mapped to a standard field and has data
      if (!mappedColumns.has(header) && rawData[header] && rawData[header].trim()) {
        // Clean the field name for display
        const fieldName = header.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').trim();
        const capitalizedName = fieldName.charAt(0).toUpperCase() + fieldName.slice(1).toLowerCase();

        cleaned.customFields[capitalizedName] = rawData[header].trim();
      }
    });
  }

  return cleaned;
};

// Helper function to update list contact count
const updateListContactCount = async (listId) => {
  try {
    const [lists, contacts] = await Promise.all([
      readJsonFile(LISTS_FILE),
      readJsonFile(CONTACTS_FILE)
    ]);

    const listIndex = lists.findIndex(l => l.id === listId);
    if (listIndex !== -1) {
      const contactCount = contacts.filter(c => c.listId === listId).length;
      lists[listIndex].contactCount = contactCount;
      lists[listIndex].updatedAt = new Date().toISOString();
      await writeJsonFile(LISTS_FILE, lists);
    }
  } catch (error) {
    console.error('Failed to update list contact count:', error);
  }
};

// POST /api/import/csv - Import contacts from CSV
router.post('/csv', upload.single('csvFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No CSV file uploaded' });
  }

  const results = [];
  const errors = [];
  const warnings = [];
  let lineNumber = 1; // Start from 1 (header line)
  let columnMapping = null;
  let detectedHeaders = [];

  // Get target list ID and custom mapping from request body
  const targetListId = req.body.listId || 'default';
  const customMapping = req.body.mapping ? JSON.parse(req.body.mapping) : null;

  try {
    const existingContacts = await readJsonFile(CONTACTS_FILE);
    const existingEmails = new Set(existingContacts.map(c => c.email.toLowerCase()));

    // Parse CSV file
    await new Promise((resolve, reject) => {
      fs.createReadStream(req.file.path)
        .pipe(csv())
        .on('headers', (headers) => {
          detectedHeaders = headers;
          columnMapping = customMapping || detectColumnMapping(headers);

          // Add warning if important fields are missing
          const hasPersonName = columnMapping.name || columnMapping.firstName || columnMapping.lastName;
          const hasCompanyName = columnMapping.company;

          if (!hasPersonName && !hasCompanyName) {
            warnings.push('No name or company column detected. Please ensure your CSV has either personal names or company names.');
          }
          if (!columnMapping.email) {
            warnings.push('No email column detected. Email is required for all contacts.');
          }

          // Helpful suggestions
          if (hasCompanyName && !hasPersonName) {
            warnings.push('Detected company names but no personal names. This is fine for business contacts.');
          }
        })
        .on('data', (data) => {
          lineNumber++;

          // Clean and map the data
          const contactData = cleanContactData(data, columnMapping, detectedHeaders);

          // Set the target list ID
          contactData.listId = targetListId;

          // Validate required fields - more flexible for business contacts
          const hasPersonName = contactData.name.trim() || contactData.firstName.trim() || contactData.lastName.trim();
          const hasCompanyName = contactData.company.trim();

          // Accept either personal name OR company name
          if (!hasPersonName && !hasCompanyName) {
            errors.push({
              line: lineNumber,
              error: 'Either a person name or company name is required',
              data: contactData,
              rawData: data
            });
            return;
          }

          // If no personal name but has company, use company as the name
          if (!hasPersonName && hasCompanyName) {
            contactData.name = contactData.company;
          }

          if (!contactData.email.trim()) {
            errors.push({
              line: lineNumber,
              error: 'Email is required but was empty or not found',
              data: contactData,
              rawData: data
            });
            return;
          }

          // Check for duplicate email
          if (existingEmails.has(contactData.email.toLowerCase())) {
            errors.push({
              line: lineNumber,
              error: 'Email already exists in the system',
              data: contactData,
              rawData: data
            });
            return;
          }

          // Create new contact
          const newContact = new Contact(contactData);
          if (newContact.isValid()) {
            results.push(newContact.toJSON());
            existingEmails.add(newContact.email.toLowerCase());
          } else {
            errors.push({
              line: lineNumber,
              error: 'Contact data validation failed',
              data: contactData,
              rawData: data
            });
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // Save valid contacts
    if (results.length > 0) {
      const allContacts = [...existingContacts, ...results];
      await writeJsonFile(CONTACTS_FILE, allContacts);

      // Update list contact count
      await updateListContactCount(targetListId);
    }

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json({
      message: 'CSV import completed',
      imported: results.length,
      errors: errors.length,
      warnings: warnings.length,
      columnMapping: columnMapping,
      detectedHeaders: detectedHeaders,
      details: {
        successful: results,
        failed: errors,
        warnings: warnings
      }
    });

  } catch (error) {
    // Clean up uploaded file on error
    if (fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    
    console.error('CSV import error:', error);
    res.status(500).json({ error: 'Failed to process CSV file' });
  }
});

// GET /api/import/template - Download CSV template
router.get('/template', (req, res) => {
  const csvTemplate = `firstName,lastName,email,phone,company,companyType,title,website,linkedinUrl,instagramUrl,facebookUrl,address,city,state,zip,country,status,notes
John,Doe,<EMAIL>,555-0123,Tech Corp,startup,Software Engineer,https://techcorp.com,https://linkedin.com/in/johndoe,https://instagram.com/johndoe,https://facebook.com/johndoe,123 Main St,San Francisco,CA,94105,USA,active,Great developer
Jane,Smith,<EMAIL>,555-0456,Design Studio,small_business,UX Designer,https://designstudio.com,https://linkedin.com/in/janesmith,,,456 Oak Ave,New York,NY,10001,USA,pending,Talented designer
Bob,Johnson,<EMAIL>,(*************,Enterprise Inc,enterprise,Project Manager,https://enterprise.com,https://linkedin.com/in/bobjohnson,,,789 Pine St,Chicago,IL,60601,USA,active,
Alice,Brown,<EMAIL>,+1-************,Green Nonprofit,nonprofit,Director,https://greennp.org,,,https://facebook.com/greennp,321 Elm St,Austin,TX,78701,USA,archived,Environmental focus
Mike,Wilson,<EMAIL>,************,Wilson Consulting,consulting,Consultant,https://wilsonconsulting.com,https://linkedin.com/in/mikewilson,,,654 Maple Dr,Seattle,WA,98101,USA,active,Business consultant`;

  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', 'attachment; filename="contacts_template.csv"');
  res.send(csvTemplate);
});

// POST /api/import/analyze - Analyze CSV structure and suggest column mappings
router.post('/analyze', upload.single('csvFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No CSV file uploaded' });
  }

  let detectedHeaders = [];
  let sampleData = [];
  let lineNumber = 0;
  const maxSampleRows = 5;

  try {
    // Parse CSV file to get headers and sample data
    await new Promise((resolve, reject) => {
      fs.createReadStream(req.file.path)
        .pipe(csv())
        .on('headers', (headers) => {
          detectedHeaders = headers;
        })
        .on('data', (data) => {
          lineNumber++;
          if (sampleData.length < maxSampleRows) {
            sampleData.push(data);
          }
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // Generate automatic mapping suggestions
    const suggestedMapping = detectColumnMapping(detectedHeaders);

    // Analyze data types and patterns
    const columnAnalysis = detectedHeaders.map(header => {
      const values = sampleData.map(row => row[header]).filter(v => v && v.trim());
      const analysis = {
        header,
        sampleValues: values.slice(0, 3),
        hasEmailPattern: values.some(v => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v)),
        hasPhonePattern: values.some(v => /[\d\-\(\)\+\s\.]{7,}/.test(v)),
        hasNamePattern: values.some(v => /^[A-Za-z\s\-'\.]+$/.test(v) && v.length > 1),
        hasCompanyPattern: values.some(v => {
          const companyKeywords = ['inc', 'llc', 'corp', 'ltd', 'company', 'co', 'group', 'agency', 'studio', 'firm', 'solutions', 'services', 'consulting'];
          return companyKeywords.some(keyword => v.toLowerCase().includes(keyword));
        }),
        hasUrlPattern: values.some(v => {
          const urlPattern = /^https?:\/\/|www\.|\.com|\.org|\.net|\.io|\.co/i;
          return urlPattern.test(v);
        }),
        hasSocialPattern: values.some(v => {
          const socialPatterns = [
            'linkedin.com', 'facebook.com', 'instagram.com', 'twitter.com', 'x.com',
            'youtube.com', 'tiktok.com', 'snapchat.com', 'pinterest.com'
          ];
          return socialPatterns.some(pattern => v.toLowerCase().includes(pattern));
        }),
        hasLinkedInPattern: values.some(v => v.toLowerCase().includes('linkedin.com')),
        hasFacebookPattern: values.some(v => v.toLowerCase().includes('facebook.com')),
        hasInstagramPattern: values.some(v => v.toLowerCase().includes('instagram.com')),
        isEmpty: values.length === 0,
        uniqueValues: [...new Set(values)].length,
        avgLength: values.length > 0 ? values.reduce((sum, v) => sum + v.length, 0) / values.length : 0
      };
      return analysis;
    });

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json({
      message: 'CSV analysis completed',
      detectedHeaders,
      suggestedMapping,
      columnAnalysis,
      sampleData: sampleData.slice(0, 3),
      totalRows: lineNumber,
      availableFields: Object.keys(detectColumnMapping([])) // Get all possible field names
    });

  } catch (error) {
    // Clean up uploaded file on error
    if (fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    console.error('CSV analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze CSV file' });
  }
});

// POST /api/import/preview - Preview CSV data with custom mapping
router.post('/preview', upload.single('csvFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ error: 'No CSV file uploaded' });
  }

  const preview = [];
  const errors = [];
  let lineNumber = 1;
  let columnMapping = null;
  let detectedHeaders = [];
  const maxPreviewRows = 10;

  // Get custom mapping from request body
  const customMapping = req.body.mapping ? JSON.parse(req.body.mapping) : null;

  try {
    // Parse CSV file for preview
    await new Promise((resolve, reject) => {
      fs.createReadStream(req.file.path)
        .pipe(csv())
        .on('headers', (headers) => {
          detectedHeaders = headers;
          columnMapping = customMapping || detectColumnMapping(headers);
        })
        .on('data', (data) => {
          lineNumber++;

          if (preview.length >= maxPreviewRows) {
            return; // Stop after max preview rows
          }

          // Clean and map the data
          const contactData = cleanContactData(data, columnMapping, detectedHeaders);

          preview.push({
            line: lineNumber,
            original: data,
            processed: contactData,
            valid: contactData.name.trim() !== '' && contactData.email.trim() !== ''
          });
        })
        .on('end', resolve)
        .on('error', reject);
    });

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json({
      message: 'CSV preview generated',
      columnMapping: columnMapping,
      detectedHeaders: detectedHeaders,
      preview: preview,
      totalRows: lineNumber - 1
    });

  } catch (error) {
    // Clean up uploaded file on error
    if (fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    console.error('CSV preview error:', error);
    res.status(500).json({ error: 'Failed to preview CSV file' });
  }
});

module.exports = router;
