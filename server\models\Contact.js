const { v4: uuidv4 } = require('uuid');

class Contact {
  constructor(data) {
    this.id = data.id || uuidv4();
    this.firstName = data.firstName || '';
    this.lastName = data.lastName || '';
    this.name = data.name || this.getFullName(); // Computed from first/last name
    this.email = data.email || '';
    this.phone = data.phone || '';
    this.status = data.status || 'active'; // active, pending, archived
    this.listId = data.listId || 'default'; // Associated contact list

    // Company information
    this.company = data.company || '';
    this.companyType = data.companyType || ''; // startup, enterprise, nonprofit, etc.
    this.title = data.title || '';

    // URLs and social media
    this.website = data.website || '';
    this.linkedinUrl = data.linkedinUrl || '';
    this.instagramUrl = data.instagramUrl || '';
    this.facebookUrl = data.facebookUrl || '';

    // Address information
    this.address = data.address || '';
    this.city = data.city || '';
    this.state = data.state || '';
    this.zip = data.zip || '';
    this.country = data.country || '';

    // Additional fields
    this.notes = data.notes || '';

    // Custom fields for miscellaneous data
    this.customFields = data.customFields || {};

    this.lastContactDate = data.lastContactDate || null;
    this.conversationCount = data.conversationCount || 0;
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
  }

  // Get full name from first and last name
  getFullName() {
    return `${this.firstName} ${this.lastName}`.trim();
  }

  // Validation - flexible for both personal and business contacts
  isValid() {
    const hasPersonName = this.firstName.trim() !== '' || this.lastName.trim() !== '' || this.name.trim() !== '';
    const hasCompanyName = this.company.trim() !== '';
    const hasEmail = this.email.trim() !== '';

    // Must have email and either a person name or company name
    return hasEmail && (hasPersonName || hasCompanyName);
  }

  // Update fields
  update(data) {
    if (data.firstName !== undefined) this.firstName = data.firstName;
    if (data.lastName !== undefined) this.lastName = data.lastName;
    if (data.name !== undefined) this.name = data.name;
    if (data.email !== undefined) this.email = data.email;
    if (data.phone !== undefined) this.phone = data.phone;
    if (data.status !== undefined) this.status = data.status;
    if (data.listId !== undefined) this.listId = data.listId;

    // Company information
    if (data.company !== undefined) this.company = data.company;
    if (data.companyType !== undefined) this.companyType = data.companyType;
    if (data.title !== undefined) this.title = data.title;

    // URLs and social media
    if (data.website !== undefined) this.website = data.website;
    if (data.linkedinUrl !== undefined) this.linkedinUrl = data.linkedinUrl;
    if (data.instagramUrl !== undefined) this.instagramUrl = data.instagramUrl;
    if (data.facebookUrl !== undefined) this.facebookUrl = data.facebookUrl;

    // Address information
    if (data.address !== undefined) this.address = data.address;
    if (data.city !== undefined) this.city = data.city;
    if (data.state !== undefined) this.state = data.state;
    if (data.zip !== undefined) this.zip = data.zip;
    if (data.country !== undefined) this.country = data.country;

    // Additional fields
    if (data.notes !== undefined) this.notes = data.notes;

    // Custom fields
    if (data.customFields !== undefined) this.customFields = data.customFields;

    if (data.lastContactDate !== undefined) this.lastContactDate = data.lastContactDate;
    if (data.conversationCount !== undefined) this.conversationCount = data.conversationCount;

    // Update computed name if first/last name changed
    if (data.firstName !== undefined || data.lastName !== undefined) {
      this.name = this.getFullName();
    }

    this.updatedAt = new Date().toISOString();
  }

  // Convert to plain object
  toJSON() {
    return {
      id: this.id,
      firstName: this.firstName,
      lastName: this.lastName,
      name: this.name || this.getFullName(),
      email: this.email,
      phone: this.phone,
      status: this.status,
      listId: this.listId,

      // Company information
      company: this.company,
      companyType: this.companyType,
      title: this.title,

      // URLs and social media
      website: this.website,
      linkedinUrl: this.linkedinUrl,
      instagramUrl: this.instagramUrl,
      facebookUrl: this.facebookUrl,

      // Address information
      address: this.address,
      city: this.city,
      state: this.state,
      zip: this.zip,
      country: this.country,

      // Additional fields
      notes: this.notes,

      // Custom fields
      customFields: this.customFields,

      lastContactDate: this.lastContactDate,
      conversationCount: this.conversationCount,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

module.exports = Contact;
