import React, { useState, useEffect } from 'react';

const ColumnMapper = ({ analysis, onMappingChange, onClose }) => {
  const [mapping, setMapping] = useState({});
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Available contact fields
  const contactFields = {
    firstName: 'First Name',
    lastName: 'Last Name',
    name: 'Full Name',
    email: 'Email Address',
    phone: 'Phone Number',
    status: 'Status',
    company: 'Company',
    companyType: 'Company Type',
    title: 'Job Title',
    website: 'Website URL',
    linkedinUrl: 'LinkedIn URL',
    instagramUrl: 'Instagram URL',
    facebookUrl: 'Facebook URL',
    address: 'Address',
    city: 'City',
    state: 'State/Province',
    zip: 'ZIP/Postal Code',
    country: 'Country',
    notes: 'Notes',
    '': '-- Do not import --'
  };

  useEffect(() => {
    // Initialize with suggested mapping
    setMapping(analysis.suggestedMapping || {});
  }, [analysis]);

  const handleMappingChange = (csvColumn, contactField) => {
    const newMapping = { ...mapping };
    
    // Remove this contact field from other CSV columns
    Object.keys(newMapping).forEach(key => {
      if (newMapping[key] === contactField && key !== csvColumn) {
        newMapping[key] = '';
      }
    });
    
    newMapping[csvColumn] = contactField;
    setMapping(newMapping);
  };

  const getFieldRecommendation = (columnAnalysis) => {
    const { header, hasEmailPattern, hasPhonePattern, hasNamePattern, sampleValues } = columnAnalysis;
    
    if (hasEmailPattern) return 'email';
    if (hasPhonePattern) return 'phone';
    if (hasNamePattern) {
      const headerLower = header.toLowerCase();
      if (headerLower.includes('first')) return 'firstName';
      if (headerLower.includes('last')) return 'lastName';
      return 'name';
    }
    
    return '';
  };

  const getConfidenceLevel = (csvColumn, suggestedField) => {
    const columnAnalysis = analysis.columnAnalysis.find(c => c.header === csvColumn);
    if (!columnAnalysis) return 'low';
    
    const { hasEmailPattern, hasPhonePattern, hasNamePattern } = columnAnalysis;
    
    if (suggestedField === 'email' && hasEmailPattern) return 'high';
    if (suggestedField === 'phone' && hasPhonePattern) return 'high';
    if (['firstName', 'lastName', 'name'].includes(suggestedField) && hasNamePattern) return 'medium';
    
    return 'low';
  };

  const handleApplyMapping = () => {
    onMappingChange(mapping);
    onClose();
  };

  return (
    <div className="modal-overlay" style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '2rem',
        borderRadius: '8px',
        width: '90%',
        maxWidth: '800px',
        maxHeight: '90vh',
        overflow: 'auto'
      }}>
        <h3>Map CSV Columns to Contact Fields</h3>
        <p style={{ color: '#6c757d', marginBottom: '2rem' }}>
          Match your CSV columns to the appropriate contact fields. We've made some suggestions based on your data.
        </p>

        <div style={{ marginBottom: '2rem' }}>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: '1fr 1fr 1fr 1fr',
            gap: '1rem',
            alignItems: 'center',
            padding: '0.75rem',
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            fontWeight: 'bold',
            fontSize: '0.9rem'
          }}>
            <div>CSV Column</div>
            <div>Sample Data</div>
            <div>Map to Field</div>
            <div>Confidence</div>
          </div>

          {analysis.columnAnalysis.map((columnAnalysis, index) => {
            const csvColumn = columnAnalysis.header;
            const currentMapping = mapping[csvColumn] || '';
            const confidence = getConfidenceLevel(csvColumn, currentMapping);
            
            return (
              <div key={index} style={{ 
                display: 'grid', 
                gridTemplateColumns: '1fr 1fr 1fr 1fr',
                gap: '1rem',
                alignItems: 'center',
                padding: '0.75rem',
                borderBottom: '1px solid #dee2e6',
                fontSize: '0.9rem'
              }}>
                <div style={{ fontWeight: 'bold' }}>
                  {csvColumn}
                </div>
                
                <div style={{ fontSize: '0.8rem', color: '#6c757d' }}>
                  {columnAnalysis.sampleValues.length > 0 ? (
                    columnAnalysis.sampleValues.map((value, i) => (
                      <div key={i} style={{ 
                        overflow: 'hidden', 
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        maxWidth: '150px'
                      }}>
                        "{value}"
                      </div>
                    ))
                  ) : (
                    <em>No data</em>
                  )}
                </div>
                
                <div>
                  <select
                    value={currentMapping}
                    onChange={(e) => handleMappingChange(csvColumn, e.target.value)}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      border: '1px solid #ddd',
                      borderRadius: '4px',
                      fontSize: '0.9rem'
                    }}
                  >
                    {Object.entries(contactFields).map(([value, label]) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <span style={{
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px',
                    fontSize: '0.8rem',
                    fontWeight: 'bold',
                    backgroundColor: 
                      confidence === 'high' ? '#d4edda' :
                      confidence === 'medium' ? '#fff3cd' : '#f8d7da',
                    color:
                      confidence === 'high' ? '#155724' :
                      confidence === 'medium' ? '#856404' : '#721c24'
                  }}>
                    {confidence.toUpperCase()}
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Advanced options */}
        <div style={{ marginBottom: '2rem' }}>
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            style={{
              background: 'none',
              border: 'none',
              color: '#3498db',
              textDecoration: 'underline',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced Options
          </button>

          {showAdvanced && (
            <div style={{ 
              marginTop: '1rem',
              padding: '1rem',
              backgroundColor: '#f8f9fa',
              borderRadius: '4px'
            }}>
              <h4>Mapping Summary</h4>
              <div style={{ fontSize: '0.9rem' }}>
                {Object.entries(mapping).filter(([, field]) => field).map(([csvCol, field]) => (
                  <div key={csvCol} style={{ marginBottom: '0.25rem' }}>
                    <strong>{csvCol}</strong> → {contactFields[field]}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div style={{ display: 'flex', gap: '1rem' }}>
          <button
            onClick={handleApplyMapping}
            className="btn btn-primary"
            style={{ flex: 1 }}
          >
            Apply Mapping & Continue
          </button>
          <button
            onClick={onClose}
            className="btn"
            style={{ 
              flex: 1,
              backgroundColor: '#6c757d',
              color: 'white'
            }}
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default ColumnMapper;
