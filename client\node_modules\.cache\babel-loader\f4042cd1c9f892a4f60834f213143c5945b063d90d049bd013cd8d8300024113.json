{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\email_dash\\\\client\\\\src\\\\components\\\\ContactDetail.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { contactAPI, conversationAPI } from '../services/api';\nimport ConversationForm from './ConversationForm';\nimport ContactForm from './ContactForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ContactDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [contact, setContact] = useState(null);\n  const [conversations, setConversations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showConversationForm, setShowConversationForm] = useState(false);\n  const [showContactForm, setShowContactForm] = useState(false);\n  useEffect(() => {\n    loadContactData();\n  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadContactData = async () => {\n    try {\n      setLoading(true);\n      const [contactResponse, conversationsResponse] = await Promise.all([contactAPI.getById(id), conversationAPI.getByContactId(id)]);\n      setContact(contactResponse.data);\n      setConversations(conversationsResponse.data);\n      setError(null);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleConversationCreated = () => {\n    setShowConversationForm(false);\n    loadContactData();\n  };\n  const handleContactUpdated = () => {\n    setShowContactForm(false);\n    loadContactData();\n  };\n  const formatTimestamp = timestamp => {\n    const date = new Date(timestamp);\n    return date.toLocaleString();\n  };\n  const formatDate = timestamp => {\n    const date = new Date(timestamp);\n    return date.toLocaleDateString();\n  };\n  const groupConversationsByDate = conversations => {\n    const groups = {};\n    conversations.forEach(conv => {\n      const date = formatDate(conv.timestamp);\n      if (!groups[date]) {\n        groups[date] = [];\n      }\n      groups[date].push(conv);\n    });\n    return groups;\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem'\n      },\n      children: \"Loading contact details...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem',\n        color: 'red'\n      },\n      children: [\"Error: \", error, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: loadContactData,\n        style: {\n          marginTop: '1rem'\n        },\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn\",\n        onClick: () => navigate('/'),\n        style: {\n          marginTop: '1rem',\n          marginLeft: '1rem'\n        },\n        children: \"Back to Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this);\n  }\n  if (!contact) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '2rem'\n      },\n      children: [\"Contact not found\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => navigate('/'),\n        style: {\n          marginTop: '1rem'\n        },\n        children: \"Back to Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this);\n  }\n  const conversationGroups = groupConversationsByDate(conversations);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: window.innerWidth > 768 ? 'center' : 'flex-start',\n        flexDirection: window.innerWidth > 768 ? 'row' : 'column',\n        gap: window.innerWidth > 768 ? '0' : '1rem',\n        marginBottom: '2rem',\n        padding: '1.5rem',\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn\",\n          onClick: () => navigate('/'),\n          style: {\n            marginRight: '1.5rem',\n            backgroundColor: '#6c757d',\n            color: 'white',\n            borderRadius: '8px',\n            padding: '0.5rem 1rem'\n          },\n          children: \"\\u2190 Back to Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '1.8rem',\n              fontWeight: 'bold',\n              margin: 0,\n              color: '#2c3e50'\n            },\n            children: contact.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this), contact.title && contact.company && /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              margin: '0.25rem 0 0 0',\n              color: '#6c757d',\n              fontSize: '1rem'\n            },\n            children: [contact.title, \" at \", contact.company]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => setShowContactForm(true),\n          style: {\n            borderRadius: '8px',\n            padding: '0.75rem 1.5rem'\n          },\n          children: \"\\u270F\\uFE0F Edit Contact\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'grid',\n        gridTemplateColumns: window.innerWidth > 768 ? '1fr 1fr' : '1fr',\n        gap: '2rem',\n        marginBottom: '2rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '1.5rem',\n          backgroundColor: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          height: 'fit-content'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '1rem',\n              color: '#2c3e50'\n            },\n            children: \"Contact Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '1rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: `mailto:${contact.email}`,\n                  style: {\n                    marginLeft: '0.5rem',\n                    color: '#3498db'\n                  },\n                  children: contact.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 15\n              }, this), contact.phone && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Phone:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: `tel:${contact.phone}`,\n                  style: {\n                    marginLeft: '0.5rem',\n                    color: '#3498db'\n                  },\n                  children: contact.phone\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `contact-status ${contact.status}`,\n                  style: {\n                    marginLeft: '0.5rem'\n                  },\n                  children: contact.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.9rem',\n                  color: '#6c757d'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Conversations: \", contact.conversationCount || 0]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [\"Last contact: \", contact.lastContactDate ? formatTimestamp(contact.lastContactDate) : 'Never']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 9\n        }, this), (contact.company || contact.title || contact.companyType) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '1rem',\n              color: '#2c3e50'\n            },\n            children: \"Company Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: '1fr 1fr',\n              gap: '1rem'\n            },\n            children: [contact.company && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Company:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), \" \", contact.company]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), contact.title && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Title:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), \" \", contact.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), contact.companyType && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Company Type:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this), \" \", contact.companyType]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), (contact.website || contact.linkedinUrl || contact.instagramUrl || contact.facebookUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '1rem',\n              color: '#2c3e50'\n            },\n            children: \"Online Presence\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: '1rem'\n            },\n            children: [contact.website && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: contact.website,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                padding: '0.5rem 1rem',\n                backgroundColor: '#3498db',\n                color: 'white',\n                textDecoration: 'none',\n                borderRadius: '4px',\n                fontSize: '0.9rem'\n              },\n              children: \"\\uD83C\\uDF10 Website\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), contact.linkedinUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: contact.linkedinUrl,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                padding: '0.5rem 1rem',\n                backgroundColor: '#0077b5',\n                color: 'white',\n                textDecoration: 'none',\n                borderRadius: '4px',\n                fontSize: '0.9rem'\n              },\n              children: \"\\uD83D\\uDCBC LinkedIn\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this), contact.instagramUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: contact.instagramUrl,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                padding: '0.5rem 1rem',\n                backgroundColor: '#e4405f',\n                color: 'white',\n                textDecoration: 'none',\n                borderRadius: '4px',\n                fontSize: '0.9rem'\n              },\n              children: \"\\uD83D\\uDCF7 Instagram\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this), contact.facebookUrl && /*#__PURE__*/_jsxDEV(\"a\", {\n              href: contact.facebookUrl,\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              style: {\n                padding: '0.5rem 1rem',\n                backgroundColor: '#1877f2',\n                color: 'white',\n                textDecoration: 'none',\n                borderRadius: '4px',\n                fontSize: '0.9rem'\n              },\n              children: \"\\uD83D\\uDCD8 Facebook\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), (contact.address || contact.city || contact.state || contact.zip || contact.country) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '1rem',\n              color: '#2c3e50'\n            },\n            children: \"Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [contact.address && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: contact.address\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 35\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [[contact.city, contact.state, contact.zip].filter(Boolean).join(', '), contact.country && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: contact.country\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 11\n        }, this), contact.customFields && Object.keys(contact.customFields).length > 0 && /*#__PURE__*/_jsxDEV(AdditionalDataSection, {\n          customFields: contact.customFields\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), contact.notes && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            style: {\n              marginBottom: '1rem',\n              color: '#2c3e50'\n            },\n            children: \"Notes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              fontSize: '0.9rem',\n              lineHeight: '1.5'\n            },\n            children: contact.notes\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          minHeight: '600px',\n          display: 'flex',\n          flexDirection: 'column'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderBottom: '1px solid #eee',\n            fontWeight: 'bold',\n            fontSize: '1.2rem',\n            color: '#2c3e50',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"\\uD83D\\uDCAC Conversations (\", conversations.length, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-sm btn-success\",\n            onClick: () => setShowConversationForm(true),\n            style: {\n              borderRadius: '6px',\n              padding: '0.5rem 1rem',\n              fontSize: '0.9rem'\n            },\n            children: \"+ Add\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            flex: 1,\n            overflowY: 'auto',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center',\n              padding: '3rem 2rem',\n              color: '#6c757d',\n              flex: 1,\n              display: 'flex',\n              flexDirection: 'column',\n              justifyContent: 'center',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '3rem',\n                marginBottom: '1rem'\n              },\n              children: \"\\uD83D\\uDCAC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '1.1rem',\n                marginBottom: '0.5rem'\n              },\n              children: \"No conversations yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '0.9rem'\n              },\n              children: \"Start a conversation to track your communications\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this) : Object.entries(conversationGroups).map(([date, dayConversations]) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '2rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                margin: '1rem 0',\n                padding: '0.5rem',\n                backgroundColor: '#f8f9fa',\n                borderRadius: '4px',\n                fontSize: '0.9rem',\n                color: '#6c757d'\n              },\n              children: date\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 17\n            }, this), dayConversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: conversation.direction === 'sent' ? 'flex-end' : 'flex-start',\n                marginBottom: '1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxWidth: '70%',\n                  padding: '1rem',\n                  borderRadius: '12px',\n                  backgroundColor: conversation.direction === 'sent' ? '#3498db' : '#e9ecef',\n                  color: conversation.direction === 'sent' ? 'white' : '#2c3e50'\n                },\n                children: [conversation.subject && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: 'bold',\n                    marginBottom: '0.5rem',\n                    fontSize: '0.9rem'\n                  },\n                  children: conversation.subject\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginBottom: '0.5rem'\n                  },\n                  children: conversation.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.8rem',\n                    opacity: 0.8,\n                    textAlign: 'right'\n                  },\n                  children: [new Date(conversation.timestamp).toLocaleTimeString(), conversation.status !== 'read' && /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      marginLeft: '0.5rem'\n                    },\n                    children: [\"\\u2022 \", conversation.status]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 21\n              }, this)\n            }, conversation.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this))]\n          }, date, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), showConversationForm && /*#__PURE__*/_jsxDEV(ConversationForm, {\n      contactId: id,\n      onClose: () => setShowConversationForm(false),\n      onConversationCreated: handleConversationCreated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 471,\n      columnNumber: 9\n    }, this), showContactForm && /*#__PURE__*/_jsxDEV(ContactForm, {\n      contact: contact,\n      onClose: () => setShowContactForm(false),\n      onContactCreated: handleContactUpdated\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n\n// Additional Data Section Component with Collapsible Categories\n_s(ContactDetail, \"v744PYO76t5D5eGqBWVgvHvzwXE=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ContactDetail;\nconst AdditionalDataSection = ({\n  customFields\n}) => {\n  _s2();\n  const [expandedSections, setExpandedSections] = useState({});\n  const toggleSection = sectionName => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [sectionName]: !prev[sectionName]\n    }));\n  };\n\n  // Categorize the custom fields\n  const categorizeFields = fields => {\n    const categories = {\n      'Professional Info': [],\n      'Company Details': [],\n      'Organization Info': [],\n      'AI Insights': [],\n      'Other': []\n    };\n    Object.entries(fields).forEach(([fieldName, value]) => {\n      const lowerFieldName = fieldName.toLowerCase();\n      if (lowerFieldName.includes('headline') || lowerFieldName.includes('seniority')) {\n        categories['Professional Info'].push([fieldName, value]);\n      } else if (lowerFieldName.includes('organization') && !lowerFieldName.includes('ai')) {\n        categories['Organization Info'].push([fieldName, value]);\n      } else if (lowerFieldName.includes('ai ') || lowerFieldName.startsWith('ai')) {\n        categories['AI Insights'].push([fieldName, value]);\n      } else if (lowerFieldName.includes('estimated') || lowerFieldName.includes('founded') || lowerFieldName.includes('revenue') || lowerFieldName.includes('keywords')) {\n        categories['Company Details'].push([fieldName, value]);\n      } else {\n        categories['Other'].push([fieldName, value]);\n      }\n    });\n\n    // Remove empty categories\n    return Object.fromEntries(Object.entries(categories).filter(([_, fields]) => fields.length > 0));\n  };\n  const categorizedFields = categorizeFields(customFields);\n  const renderFieldValue = value => {\n    if (typeof value === 'string' && value.length > 200) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '0.9rem',\n          lineHeight: '1.4'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            maxHeight: '100px',\n            overflowY: 'auto',\n            padding: '0.5rem',\n            backgroundColor: '#ffffff',\n            border: '1px solid #e9ecef',\n            borderRadius: '4px'\n          },\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontSize: '0.9rem',\n        lineHeight: '1.4'\n      },\n      children: value\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginBottom: '1.5rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      style: {\n        marginBottom: '1rem',\n        color: '#2c3e50'\n      },\n      children: \"Additional Information\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), Object.entries(categorizedFields).map(([categoryName, fields]) => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '1rem'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => toggleSection(categoryName),\n        style: {\n          width: '100%',\n          padding: '0.75rem',\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #dee2e6',\n          borderRadius: '6px',\n          cursor: 'pointer',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          fontSize: '1rem',\n          fontWeight: 'bold',\n          color: '#495057',\n          transition: 'all 0.2s'\n        },\n        onMouseEnter: e => {\n          e.target.style.backgroundColor = '#e9ecef';\n        },\n        onMouseLeave: e => {\n          e.target.style.backgroundColor = '#f8f9fa';\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [categoryName, \" (\", fields.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            transform: expandedSections[categoryName] ? 'rotate(180deg)' : 'rotate(0deg)',\n            transition: 'transform 0.2s'\n          },\n          children: \"\\u25BC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 590,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 565,\n        columnNumber: 11\n      }, this), expandedSections[categoryName] && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '0.5rem',\n          padding: '1rem',\n          backgroundColor: '#ffffff',\n          border: '1px solid #dee2e6',\n          borderTop: 'none',\n          borderRadius: '0 0 6px 6px',\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n          gap: '1rem'\n        },\n        children: fields.map(([fieldName, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0.75rem',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '4px',\n            border: '1px solid #e9ecef'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              fontSize: '0.9rem',\n              marginBottom: '0.5rem',\n              color: '#495057'\n            },\n            children: fieldName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 19\n          }, this), renderFieldValue(value)]\n        }, fieldName, true, {\n          fileName: _jsxFileName,\n          lineNumber: 611,\n          columnNumber: 17\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 13\n      }, this)]\n    }, categoryName, true, {\n      fileName: _jsxFileName,\n      lineNumber: 564,\n      columnNumber: 9\n    }, this))]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 560,\n    columnNumber: 5\n  }, this);\n};\n_s2(AdditionalDataSection, \"MuXrzuXOkBisxDgI2dpYJH0j/AI=\");\n_c2 = AdditionalDataSection;\nexport default ContactDetail;\nvar _c, _c2;\n$RefreshReg$(_c, \"ContactDetail\");\n$RefreshReg$(_c2, \"AdditionalDataSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "contactAPI", "conversationAPI", "ConversationForm", "ContactForm", "jsxDEV", "_jsxDEV", "ContactDetail", "_s", "id", "navigate", "contact", "setContact", "conversations", "setConversations", "loading", "setLoading", "error", "setError", "showConversationForm", "setShowConversationForm", "showContactForm", "setShowContactForm", "loadContactData", "contactResponse", "conversationsResponse", "Promise", "all", "getById", "getByContactId", "data", "err", "message", "handleConversationCreated", "handleContactUpdated", "formatTimestamp", "timestamp", "date", "Date", "toLocaleString", "formatDate", "toLocaleDateString", "groupConversationsByDate", "groups", "for<PERSON>ach", "conv", "push", "style", "textAlign", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "className", "onClick", "marginTop", "marginLeft", "conversationGroups", "max<PERSON><PERSON><PERSON>", "margin", "display", "justifyContent", "alignItems", "window", "innerWidth", "flexDirection", "gap", "marginBottom", "backgroundColor", "borderRadius", "boxShadow", "marginRight", "fontSize", "fontWeight", "name", "title", "company", "gridTemplateColumns", "height", "href", "email", "phone", "status", "conversationCount", "lastContactDate", "companyType", "website", "linkedinUrl", "instagramUrl", "facebookUrl", "flexWrap", "target", "rel", "textDecoration", "address", "city", "state", "zip", "country", "filter", "Boolean", "join", "customFields", "Object", "keys", "length", "AdditionalDataSection", "notes", "lineHeight", "minHeight", "borderBottom", "flex", "overflowY", "entries", "map", "dayConversations", "conversation", "direction", "subject", "content", "opacity", "toLocaleTimeString", "contactId", "onClose", "onConversationCreated", "onContactCreated", "_c", "_s2", "expandedSections", "setExpandedSections", "toggleSection", "sectionName", "prev", "categorizeFields", "fields", "categories", "fieldName", "value", "lowerFieldName", "toLowerCase", "includes", "startsWith", "fromEntries", "_", "categorizedFields", "renderFieldValue", "maxHeight", "border", "categoryName", "width", "cursor", "transition", "onMouseEnter", "e", "onMouseLeave", "transform", "borderTop", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/email_dash/client/src/components/ContactDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { contactAPI, conversationAPI } from '../services/api';\nimport ConversationForm from './ConversationForm';\nimport ContactForm from './ContactForm';\n\nconst ContactDetail = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const [contact, setContact] = useState(null);\n  const [conversations, setConversations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showConversationForm, setShowConversationForm] = useState(false);\n  const [showContactForm, setShowContactForm] = useState(false);\n\n  useEffect(() => {\n    loadContactData();\n  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const loadContactData = async () => {\n    try {\n      setLoading(true);\n      const [contactResponse, conversationsResponse] = await Promise.all([\n        contactAPI.getById(id),\n        conversationAPI.getByContactId(id)\n      ]);\n      \n      setContact(contactResponse.data);\n      setConversations(conversationsResponse.data);\n      setError(null);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleConversationCreated = () => {\n    setShowConversationForm(false);\n    loadContactData();\n  };\n\n  const handleContactUpdated = () => {\n    setShowContactForm(false);\n    loadContactData();\n  };\n\n  const formatTimestamp = (timestamp) => {\n    const date = new Date(timestamp);\n    return date.toLocaleString();\n  };\n\n  const formatDate = (timestamp) => {\n    const date = new Date(timestamp);\n    return date.toLocaleDateString();\n  };\n\n  const groupConversationsByDate = (conversations) => {\n    const groups = {};\n    conversations.forEach(conv => {\n      const date = formatDate(conv.timestamp);\n      if (!groups[date]) {\n        groups[date] = [];\n      }\n      groups[date].push(conv);\n    });\n    return groups;\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '2rem' }}>\n        Loading contact details...\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>\n        Error: {error}\n        <br />\n        <button className=\"btn btn-primary\" onClick={loadContactData} style={{ marginTop: '1rem' }}>\n          Retry\n        </button>\n        <button className=\"btn\" onClick={() => navigate('/')} style={{ marginTop: '1rem', marginLeft: '1rem' }}>\n          Back to Dashboard\n        </button>\n      </div>\n    );\n  }\n\n  if (!contact) {\n    return (\n      <div style={{ textAlign: 'center', padding: '2rem' }}>\n        Contact not found\n        <br />\n        <button className=\"btn btn-primary\" onClick={() => navigate('/')} style={{ marginTop: '1rem' }}>\n          Back to Dashboard\n        </button>\n      </div>\n    );\n  }\n\n  const conversationGroups = groupConversationsByDate(conversations);\n\n  return (\n    <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n      {/* Header */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: window.innerWidth > 768 ? 'center' : 'flex-start',\n        flexDirection: window.innerWidth > 768 ? 'row' : 'column',\n        gap: window.innerWidth > 768 ? '0' : '1rem',\n        marginBottom: '2rem',\n        padding: '1.5rem',\n        backgroundColor: 'white',\n        borderRadius: '12px',\n        boxShadow: '0 4px 6px rgba(0,0,0,0.1)'\n      }}>\n        <div style={{ display: 'flex', alignItems: 'center' }}>\n          <button\n            className=\"btn\"\n            onClick={() => navigate('/')}\n            style={{\n              marginRight: '1.5rem',\n              backgroundColor: '#6c757d',\n              color: 'white',\n              borderRadius: '8px',\n              padding: '0.5rem 1rem'\n            }}\n          >\n            ← Back to Dashboard\n          </button>\n          <div>\n            <h1 style={{ fontSize: '1.8rem', fontWeight: 'bold', margin: 0, color: '#2c3e50' }}>\n              {contact.name}\n            </h1>\n            {contact.title && contact.company && (\n              <p style={{ margin: '0.25rem 0 0 0', color: '#6c757d', fontSize: '1rem' }}>\n                {contact.title} at {contact.company}\n              </p>\n            )}\n          </div>\n        </div>\n        <div>\n          <button\n            className=\"btn btn-primary\"\n            onClick={() => setShowContactForm(true)}\n            style={{\n              borderRadius: '8px',\n              padding: '0.75rem 1.5rem'\n            }}\n          >\n            ✏️ Edit Contact\n          </button>\n        </div>\n      </div>\n\n      {/* Main Content - Responsive Two Column Layout */}\n      <div style={{\n        display: 'grid',\n        gridTemplateColumns: window.innerWidth > 768 ? '1fr 1fr' : '1fr',\n        gap: '2rem',\n        marginBottom: '2rem'\n      }}>\n\n        {/* Left Column - Contact Info */}\n        <div style={{\n          padding: '1.5rem',\n          backgroundColor: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          height: 'fit-content'\n        }}>\n        {/* Basic Information */}\n        <div style={{ marginBottom: '1.5rem' }}>\n          <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Contact Information</h4>\n          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>\n            <div>\n              <div style={{ marginBottom: '0.5rem' }}>\n                <strong>Email:</strong>\n                <a href={`mailto:${contact.email}`} style={{ marginLeft: '0.5rem', color: '#3498db' }}>\n                  {contact.email}\n                </a>\n              </div>\n              {contact.phone && (\n                <div style={{ marginBottom: '0.5rem' }}>\n                  <strong>Phone:</strong>\n                  <a href={`tel:${contact.phone}`} style={{ marginLeft: '0.5rem', color: '#3498db' }}>\n                    {contact.phone}\n                  </a>\n                </div>\n              )}\n            </div>\n            <div>\n              <div style={{ marginBottom: '0.5rem' }}>\n                <strong>Status:</strong>\n                <span className={`contact-status ${contact.status}`} style={{ marginLeft: '0.5rem' }}>\n                  {contact.status}\n                </span>\n              </div>\n              <div style={{ fontSize: '0.9rem', color: '#6c757d' }}>\n                <div>Conversations: {contact.conversationCount || 0}</div>\n                <div>Last contact: {contact.lastContactDate ? formatTimestamp(contact.lastContactDate) : 'Never'}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Company Information */}\n        {(contact.company || contact.title || contact.companyType) && (\n          <div style={{ marginBottom: '1.5rem' }}>\n            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Company Information</h4>\n            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>\n              {contact.company && (\n                <div style={{ marginBottom: '0.5rem' }}>\n                  <strong>Company:</strong> {contact.company}\n                </div>\n              )}\n              {contact.title && (\n                <div style={{ marginBottom: '0.5rem' }}>\n                  <strong>Title:</strong> {contact.title}\n                </div>\n              )}\n              {contact.companyType && (\n                <div style={{ marginBottom: '0.5rem' }}>\n                  <strong>Company Type:</strong> {contact.companyType}\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* URLs and Social Media */}\n        {(contact.website || contact.linkedinUrl || contact.instagramUrl || contact.facebookUrl) && (\n          <div style={{ marginBottom: '1.5rem' }}>\n            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Online Presence</h4>\n            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '1rem' }}>\n              {contact.website && (\n                <a\n                  href={contact.website}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  style={{\n                    padding: '0.5rem 1rem',\n                    backgroundColor: '#3498db',\n                    color: 'white',\n                    textDecoration: 'none',\n                    borderRadius: '4px',\n                    fontSize: '0.9rem'\n                  }}\n                >\n                  🌐 Website\n                </a>\n              )}\n              {contact.linkedinUrl && (\n                <a\n                  href={contact.linkedinUrl}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  style={{\n                    padding: '0.5rem 1rem',\n                    backgroundColor: '#0077b5',\n                    color: 'white',\n                    textDecoration: 'none',\n                    borderRadius: '4px',\n                    fontSize: '0.9rem'\n                  }}\n                >\n                  💼 LinkedIn\n                </a>\n              )}\n              {contact.instagramUrl && (\n                <a\n                  href={contact.instagramUrl}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  style={{\n                    padding: '0.5rem 1rem',\n                    backgroundColor: '#e4405f',\n                    color: 'white',\n                    textDecoration: 'none',\n                    borderRadius: '4px',\n                    fontSize: '0.9rem'\n                  }}\n                >\n                  📷 Instagram\n                </a>\n              )}\n              {contact.facebookUrl && (\n                <a\n                  href={contact.facebookUrl}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  style={{\n                    padding: '0.5rem 1rem',\n                    backgroundColor: '#1877f2',\n                    color: 'white',\n                    textDecoration: 'none',\n                    borderRadius: '4px',\n                    fontSize: '0.9rem'\n                  }}\n                >\n                  📘 Facebook\n                </a>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Address Information */}\n        {(contact.address || contact.city || contact.state || contact.zip || contact.country) && (\n          <div style={{ marginBottom: '1.5rem' }}>\n            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Address</h4>\n            <div>\n              {contact.address && <div>{contact.address}</div>}\n              <div>\n                {[contact.city, contact.state, contact.zip].filter(Boolean).join(', ')}\n                {contact.country && <div>{contact.country}</div>}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Custom Fields - Enhanced with Collapsible Sections */}\n        {contact.customFields && Object.keys(contact.customFields).length > 0 && (\n          <AdditionalDataSection customFields={contact.customFields} />\n        )}\n\n        {/* Notes */}\n        {contact.notes && (\n          <div>\n            <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Notes</h4>\n            <div style={{\n              padding: '1rem',\n              backgroundColor: '#f8f9fa',\n              borderRadius: '4px',\n              fontSize: '0.9rem',\n              lineHeight: '1.5'\n            }}>\n              {contact.notes}\n            </div>\n          </div>\n        )}\n        </div>\n\n        {/* Right Column - Conversations */}\n        <div style={{\n          backgroundColor: 'white',\n          borderRadius: '12px',\n          boxShadow: '0 4px 6px rgba(0,0,0,0.1)',\n          minHeight: '600px',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          <div style={{\n            padding: '1.5rem',\n            borderBottom: '1px solid #eee',\n            fontWeight: 'bold',\n            fontSize: '1.2rem',\n            color: '#2c3e50',\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          }}>\n            <span>💬 Conversations ({conversations.length})</span>\n            <button\n              className=\"btn btn-sm btn-success\"\n              onClick={() => setShowConversationForm(true)}\n              style={{\n                borderRadius: '6px',\n                padding: '0.5rem 1rem',\n                fontSize: '0.9rem'\n              }}\n            >\n              + Add\n            </button>\n          </div>\n\n          <div style={{\n            padding: '1.5rem',\n            flex: 1,\n            overflowY: 'auto',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            {conversations.length === 0 ? (\n              <div style={{\n                textAlign: 'center',\n                padding: '3rem 2rem',\n                color: '#6c757d',\n                flex: 1,\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'center',\n                alignItems: 'center'\n              }}>\n                <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>💬</div>\n                <div style={{ fontSize: '1.1rem', marginBottom: '0.5rem' }}>No conversations yet</div>\n                <div style={{ fontSize: '0.9rem' }}>Start a conversation to track your communications</div>\n              </div>\n            ) : (\n            Object.entries(conversationGroups).map(([date, dayConversations]) => (\n              <div key={date} style={{ marginBottom: '2rem' }}>\n                <div style={{ \n                  textAlign: 'center',\n                  margin: '1rem 0',\n                  padding: '0.5rem',\n                  backgroundColor: '#f8f9fa',\n                  borderRadius: '4px',\n                  fontSize: '0.9rem',\n                  color: '#6c757d'\n                }}>\n                  {date}\n                </div>\n                \n                {dayConversations.map((conversation) => (\n                  <div \n                    key={conversation.id}\n                    style={{\n                      display: 'flex',\n                      justifyContent: conversation.direction === 'sent' ? 'flex-end' : 'flex-start',\n                      marginBottom: '1rem'\n                    }}\n                  >\n                    <div style={{\n                      maxWidth: '70%',\n                      padding: '1rem',\n                      borderRadius: '12px',\n                      backgroundColor: conversation.direction === 'sent' ? '#3498db' : '#e9ecef',\n                      color: conversation.direction === 'sent' ? 'white' : '#2c3e50'\n                    }}>\n                      {conversation.subject && (\n                        <div style={{ \n                          fontWeight: 'bold', \n                          marginBottom: '0.5rem',\n                          fontSize: '0.9rem'\n                        }}>\n                          {conversation.subject}\n                        </div>\n                      )}\n                      <div style={{ marginBottom: '0.5rem' }}>\n                        {conversation.content}\n                      </div>\n                      <div style={{ \n                        fontSize: '0.8rem',\n                        opacity: 0.8,\n                        textAlign: 'right'\n                      }}>\n                        {new Date(conversation.timestamp).toLocaleTimeString()}\n                        {conversation.status !== 'read' && (\n                          <span style={{ marginLeft: '0.5rem' }}>\n                            • {conversation.status}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            ))\n            )}\n          </div>\n        </div>\n      </div>\n\n      {showConversationForm && (\n        <ConversationForm \n          contactId={id}\n          onClose={() => setShowConversationForm(false)}\n          onConversationCreated={handleConversationCreated}\n        />\n      )}\n\n      {showContactForm && (\n        <ContactForm \n          contact={contact}\n          onClose={() => setShowContactForm(false)}\n          onContactCreated={handleContactUpdated}\n        />\n      )}\n    </div>\n  );\n};\n\n// Additional Data Section Component with Collapsible Categories\nconst AdditionalDataSection = ({ customFields }) => {\n  const [expandedSections, setExpandedSections] = useState({});\n\n  const toggleSection = (sectionName) => {\n    setExpandedSections(prev => ({\n      ...prev,\n      [sectionName]: !prev[sectionName]\n    }));\n  };\n\n  // Categorize the custom fields\n  const categorizeFields = (fields) => {\n    const categories = {\n      'Professional Info': [],\n      'Company Details': [],\n      'Organization Info': [],\n      'AI Insights': [],\n      'Other': []\n    };\n\n    Object.entries(fields).forEach(([fieldName, value]) => {\n      const lowerFieldName = fieldName.toLowerCase();\n\n      if (lowerFieldName.includes('headline') || lowerFieldName.includes('seniority')) {\n        categories['Professional Info'].push([fieldName, value]);\n      } else if (lowerFieldName.includes('organization') && !lowerFieldName.includes('ai')) {\n        categories['Organization Info'].push([fieldName, value]);\n      } else if (lowerFieldName.includes('ai ') || lowerFieldName.startsWith('ai')) {\n        categories['AI Insights'].push([fieldName, value]);\n      } else if (lowerFieldName.includes('estimated') || lowerFieldName.includes('founded') ||\n                 lowerFieldName.includes('revenue') || lowerFieldName.includes('keywords')) {\n        categories['Company Details'].push([fieldName, value]);\n      } else {\n        categories['Other'].push([fieldName, value]);\n      }\n    });\n\n    // Remove empty categories\n    return Object.fromEntries(\n      Object.entries(categories).filter(([_, fields]) => fields.length > 0)\n    );\n  };\n\n  const categorizedFields = categorizeFields(customFields);\n\n  const renderFieldValue = (value) => {\n    if (typeof value === 'string' && value.length > 200) {\n      return (\n        <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>\n          <div style={{\n            maxHeight: '100px',\n            overflowY: 'auto',\n            padding: '0.5rem',\n            backgroundColor: '#ffffff',\n            border: '1px solid #e9ecef',\n            borderRadius: '4px'\n          }}>\n            {value}\n          </div>\n        </div>\n      );\n    }\n    return (\n      <div style={{ fontSize: '0.9rem', lineHeight: '1.4' }}>\n        {value}\n      </div>\n    );\n  };\n\n  return (\n    <div style={{ marginBottom: '1.5rem' }}>\n      <h4 style={{ marginBottom: '1rem', color: '#2c3e50' }}>Additional Information</h4>\n\n      {Object.entries(categorizedFields).map(([categoryName, fields]) => (\n        <div key={categoryName} style={{ marginBottom: '1rem' }}>\n          <button\n            onClick={() => toggleSection(categoryName)}\n            style={{\n              width: '100%',\n              padding: '0.75rem',\n              backgroundColor: '#f8f9fa',\n              border: '1px solid #dee2e6',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center',\n              fontSize: '1rem',\n              fontWeight: 'bold',\n              color: '#495057',\n              transition: 'all 0.2s'\n            }}\n            onMouseEnter={(e) => {\n              e.target.style.backgroundColor = '#e9ecef';\n            }}\n            onMouseLeave={(e) => {\n              e.target.style.backgroundColor = '#f8f9fa';\n            }}\n          >\n            <span>{categoryName} ({fields.length})</span>\n            <span style={{\n              transform: expandedSections[categoryName] ? 'rotate(180deg)' : 'rotate(0deg)',\n              transition: 'transform 0.2s'\n            }}>\n              ▼\n            </span>\n          </button>\n\n          {expandedSections[categoryName] && (\n            <div style={{\n              marginTop: '0.5rem',\n              padding: '1rem',\n              backgroundColor: '#ffffff',\n              border: '1px solid #dee2e6',\n              borderTop: 'none',\n              borderRadius: '0 0 6px 6px',\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n              gap: '1rem'\n            }}>\n              {fields.map(([fieldName, value]) => (\n                <div key={fieldName} style={{\n                  padding: '0.75rem',\n                  backgroundColor: '#f8f9fa',\n                  borderRadius: '4px',\n                  border: '1px solid #e9ecef'\n                }}>\n                  <div style={{\n                    fontWeight: 'bold',\n                    fontSize: '0.9rem',\n                    marginBottom: '0.5rem',\n                    color: '#495057'\n                  }}>\n                    {fieldName}\n                  </div>\n                  {renderFieldValue(value)}\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default ContactDetail;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,UAAU,EAAEC,eAAe,QAAQ,iBAAiB;AAC7D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAG,CAAC,GAAGV,SAAS,CAAC,CAAC;EAC1B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,aAAa,EAAEC,gBAAgB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,KAAK,EAAEC,QAAQ,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACsB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACwB,eAAe,EAAEC,kBAAkB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAE7DC,SAAS,CAAC,MAAM;IACdyB,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACd,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEV,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM,CAACQ,eAAe,EAAEC,qBAAqB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACjE1B,UAAU,CAAC2B,OAAO,CAACnB,EAAE,CAAC,EACtBP,eAAe,CAAC2B,cAAc,CAACpB,EAAE,CAAC,CACnC,CAAC;MAEFG,UAAU,CAACY,eAAe,CAACM,IAAI,CAAC;MAChChB,gBAAgB,CAACW,qBAAqB,CAACK,IAAI,CAAC;MAC5CZ,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOa,GAAG,EAAE;MACZb,QAAQ,CAACa,GAAG,CAACC,OAAO,CAAC;IACvB,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,yBAAyB,GAAGA,CAAA,KAAM;IACtCb,uBAAuB,CAAC,KAAK,CAAC;IAC9BG,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAMW,oBAAoB,GAAGA,CAAA,KAAM;IACjCZ,kBAAkB,CAAC,KAAK,CAAC;IACzBC,eAAe,CAAC,CAAC;EACnB,CAAC;EAED,MAAMY,eAAe,GAAIC,SAAS,IAAK;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAChC,OAAOC,IAAI,CAACE,cAAc,CAAC,CAAC;EAC9B,CAAC;EAED,MAAMC,UAAU,GAAIJ,SAAS,IAAK;IAChC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAChC,OAAOC,IAAI,CAACI,kBAAkB,CAAC,CAAC;EAClC,CAAC;EAED,MAAMC,wBAAwB,GAAI7B,aAAa,IAAK;IAClD,MAAM8B,MAAM,GAAG,CAAC,CAAC;IACjB9B,aAAa,CAAC+B,OAAO,CAACC,IAAI,IAAI;MAC5B,MAAMR,IAAI,GAAGG,UAAU,CAACK,IAAI,CAACT,SAAS,CAAC;MACvC,IAAI,CAACO,MAAM,CAACN,IAAI,CAAC,EAAE;QACjBM,MAAM,CAACN,IAAI,CAAC,GAAG,EAAE;MACnB;MACAM,MAAM,CAACN,IAAI,CAAC,CAACS,IAAI,CAACD,IAAI,CAAC;IACzB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC;EAED,IAAI5B,OAAO,EAAE;IACX,oBACET,OAAA;MAAKyC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,EAAC;IAEtD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,IAAIrC,KAAK,EAAE;IACT,oBACEX,OAAA;MAAKyC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE,MAAM;QAAEM,KAAK,EAAE;MAAM,CAAE;MAAAL,QAAA,GAAC,SAC3D,EAACjC,KAAK,eACbX,OAAA;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNhD,OAAA;QAAQkD,SAAS,EAAC,iBAAiB;QAACC,OAAO,EAAElC,eAAgB;QAACwB,KAAK,EAAE;UAAEW,SAAS,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAE5F;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACThD,OAAA;QAAQkD,SAAS,EAAC,KAAK;QAACC,OAAO,EAAEA,CAAA,KAAM/C,QAAQ,CAAC,GAAG,CAAE;QAACqC,KAAK,EAAE;UAAEW,SAAS,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAT,QAAA,EAAC;MAExG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,IAAI,CAAC3C,OAAO,EAAE;IACZ,oBACEL,OAAA;MAAKyC,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,GAAC,mBAEpD,eAAA5C,OAAA;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNhD,OAAA;QAAQkD,SAAS,EAAC,iBAAiB;QAACC,OAAO,EAAEA,CAAA,KAAM/C,QAAQ,CAAC,GAAG,CAAE;QAACqC,KAAK,EAAE;UAAEW,SAAS,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAEhG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,MAAMM,kBAAkB,GAAGlB,wBAAwB,CAAC7B,aAAa,CAAC;EAElE,oBACEP,OAAA;IAAKyC,KAAK,EAAE;MAAEc,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAZ,QAAA,gBAEnD5C,OAAA;MAAKyC,KAAK,EAAE;QACVgB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAEC,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,QAAQ,GAAG,YAAY;QAC7DC,aAAa,EAAEF,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,KAAK,GAAG,QAAQ;QACzDE,GAAG,EAAEH,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM;QAC3CG,YAAY,EAAE,MAAM;QACpBrB,OAAO,EAAE,QAAQ;QACjBsB,eAAe,EAAE,OAAO;QACxBC,YAAY,EAAE,MAAM;QACpBC,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,gBACA5C,OAAA;QAAKyC,KAAK,EAAE;UAAEgB,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE;QAAS,CAAE;QAAAf,QAAA,gBACpD5C,OAAA;UACEkD,SAAS,EAAC,KAAK;UACfC,OAAO,EAAEA,CAAA,KAAM/C,QAAQ,CAAC,GAAG,CAAE;UAC7BqC,KAAK,EAAE;YACL2B,WAAW,EAAE,QAAQ;YACrBH,eAAe,EAAE,SAAS;YAC1BhB,KAAK,EAAE,OAAO;YACdiB,YAAY,EAAE,KAAK;YACnBvB,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThD,OAAA;UAAA4C,QAAA,gBACE5C,OAAA;YAAIyC,KAAK,EAAE;cAAE4B,QAAQ,EAAE,QAAQ;cAAEC,UAAU,EAAE,MAAM;cAAEd,MAAM,EAAE,CAAC;cAAEP,KAAK,EAAE;YAAU,CAAE;YAAAL,QAAA,EAChFvC,OAAO,CAACkE;UAAI;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EACJ3C,OAAO,CAACmE,KAAK,IAAInE,OAAO,CAACoE,OAAO,iBAC/BzE,OAAA;YAAGyC,KAAK,EAAE;cAAEe,MAAM,EAAE,eAAe;cAAEP,KAAK,EAAE,SAAS;cAAEoB,QAAQ,EAAE;YAAO,CAAE;YAAAzB,QAAA,GACvEvC,OAAO,CAACmE,KAAK,EAAC,MAAI,EAACnE,OAAO,CAACoE,OAAO;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNhD,OAAA;QAAA4C,QAAA,eACE5C,OAAA;UACEkD,SAAS,EAAC,iBAAiB;UAC3BC,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAC,IAAI,CAAE;UACxCyB,KAAK,EAAE;YACLyB,YAAY,EAAE,KAAK;YACnBvB,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhD,OAAA;MAAKyC,KAAK,EAAE;QACVgB,OAAO,EAAE,MAAM;QACfiB,mBAAmB,EAAEd,MAAM,CAACC,UAAU,GAAG,GAAG,GAAG,SAAS,GAAG,KAAK;QAChEE,GAAG,EAAE,MAAM;QACXC,YAAY,EAAE;MAChB,CAAE;MAAApB,QAAA,gBAGA5C,OAAA;QAAKyC,KAAK,EAAE;UACVE,OAAO,EAAE,QAAQ;UACjBsB,eAAe,EAAE,OAAO;UACxBC,YAAY,EAAE,MAAM;UACpBC,SAAS,EAAE,2BAA2B;UACtCQ,MAAM,EAAE;QACV,CAAE;QAAA/B,QAAA,gBAEF5C,OAAA;UAAKyC,KAAK,EAAE;YAAEuB,YAAY,EAAE;UAAS,CAAE;UAAApB,QAAA,gBACrC5C,OAAA;YAAIyC,KAAK,EAAE;cAAEuB,YAAY,EAAE,MAAM;cAAEf,KAAK,EAAE;YAAU,CAAE;YAAAL,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EhD,OAAA;YAAKyC,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEiB,mBAAmB,EAAE,SAAS;cAAEX,GAAG,EAAE;YAAO,CAAE;YAAAnB,QAAA,gBAC3E5C,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAKyC,KAAK,EAAE;kBAAEuB,YAAY,EAAE;gBAAS,CAAE;gBAAApB,QAAA,gBACrC5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvBhD,OAAA;kBAAG4E,IAAI,EAAE,UAAUvE,OAAO,CAACwE,KAAK,EAAG;kBAACpC,KAAK,EAAE;oBAAEY,UAAU,EAAE,QAAQ;oBAAEJ,KAAK,EAAE;kBAAU,CAAE;kBAAAL,QAAA,EACnFvC,OAAO,CAACwE;gBAAK;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL3C,OAAO,CAACyE,KAAK,iBACZ9E,OAAA;gBAAKyC,KAAK,EAAE;kBAAEuB,YAAY,EAAE;gBAAS,CAAE;gBAAApB,QAAA,gBACrC5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvBhD,OAAA;kBAAG4E,IAAI,EAAE,OAAOvE,OAAO,CAACyE,KAAK,EAAG;kBAACrC,KAAK,EAAE;oBAAEY,UAAU,EAAE,QAAQ;oBAAEJ,KAAK,EAAE;kBAAU,CAAE;kBAAAL,QAAA,EAChFvC,OAAO,CAACyE;gBAAK;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNhD,OAAA;cAAA4C,QAAA,gBACE5C,OAAA;gBAAKyC,KAAK,EAAE;kBAAEuB,YAAY,EAAE;gBAAS,CAAE;gBAAApB,QAAA,gBACrC5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxBhD,OAAA;kBAAMkD,SAAS,EAAE,kBAAkB7C,OAAO,CAAC0E,MAAM,EAAG;kBAACtC,KAAK,EAAE;oBAAEY,UAAU,EAAE;kBAAS,CAAE;kBAAAT,QAAA,EAClFvC,OAAO,CAAC0E;gBAAM;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhD,OAAA;gBAAKyC,KAAK,EAAE;kBAAE4B,QAAQ,EAAE,QAAQ;kBAAEpB,KAAK,EAAE;gBAAU,CAAE;gBAAAL,QAAA,gBACnD5C,OAAA;kBAAA4C,QAAA,GAAK,iBAAe,EAACvC,OAAO,CAAC2E,iBAAiB,IAAI,CAAC;gBAAA;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1DhD,OAAA;kBAAA4C,QAAA,GAAK,gBAAc,EAACvC,OAAO,CAAC4E,eAAe,GAAGpD,eAAe,CAACxB,OAAO,CAAC4E,eAAe,CAAC,GAAG,OAAO;gBAAA;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL,CAAC3C,OAAO,CAACoE,OAAO,IAAIpE,OAAO,CAACmE,KAAK,IAAInE,OAAO,CAAC6E,WAAW,kBACvDlF,OAAA;UAAKyC,KAAK,EAAE;YAAEuB,YAAY,EAAE;UAAS,CAAE;UAAApB,QAAA,gBACrC5C,OAAA;YAAIyC,KAAK,EAAE;cAAEuB,YAAY,EAAE,MAAM;cAAEf,KAAK,EAAE;YAAU,CAAE;YAAAL,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/EhD,OAAA;YAAKyC,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAEiB,mBAAmB,EAAE,SAAS;cAAEX,GAAG,EAAE;YAAO,CAAE;YAAAnB,QAAA,GAC1EvC,OAAO,CAACoE,OAAO,iBACdzE,OAAA;cAAKyC,KAAK,EAAE;gBAAEuB,YAAY,EAAE;cAAS,CAAE;cAAApB,QAAA,gBACrC5C,OAAA;gBAAA4C,QAAA,EAAQ;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3C,OAAO,CAACoE,OAAO;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACN,EACA3C,OAAO,CAACmE,KAAK,iBACZxE,OAAA;cAAKyC,KAAK,EAAE;gBAAEuB,YAAY,EAAE;cAAS,CAAE;cAAApB,QAAA,gBACrC5C,OAAA;gBAAA4C,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3C,OAAO,CAACmE,KAAK;YAAA;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACN,EACA3C,OAAO,CAAC6E,WAAW,iBAClBlF,OAAA;cAAKyC,KAAK,EAAE;gBAAEuB,YAAY,EAAE;cAAS,CAAE;cAAApB,QAAA,gBACrC5C,OAAA;gBAAA4C,QAAA,EAAQ;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3C,OAAO,CAAC6E,WAAW;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA,CAAC3C,OAAO,CAAC8E,OAAO,IAAI9E,OAAO,CAAC+E,WAAW,IAAI/E,OAAO,CAACgF,YAAY,IAAIhF,OAAO,CAACiF,WAAW,kBACrFtF,OAAA;UAAKyC,KAAK,EAAE;YAAEuB,YAAY,EAAE;UAAS,CAAE;UAAApB,QAAA,gBACrC5C,OAAA;YAAIyC,KAAK,EAAE;cAAEuB,YAAY,EAAE,MAAM;cAAEf,KAAK,EAAE;YAAU,CAAE;YAAAL,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3EhD,OAAA;YAAKyC,KAAK,EAAE;cAAEgB,OAAO,EAAE,MAAM;cAAE8B,QAAQ,EAAE,MAAM;cAAExB,GAAG,EAAE;YAAO,CAAE;YAAAnB,QAAA,GAC5DvC,OAAO,CAAC8E,OAAO,iBACdnF,OAAA;cACE4E,IAAI,EAAEvE,OAAO,CAAC8E,OAAQ;cACtBK,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBhD,KAAK,EAAE;gBACLE,OAAO,EAAE,aAAa;gBACtBsB,eAAe,EAAE,SAAS;gBAC1BhB,KAAK,EAAE,OAAO;gBACdyC,cAAc,EAAE,MAAM;gBACtBxB,YAAY,EAAE,KAAK;gBACnBG,QAAQ,EAAE;cACZ,CAAE;cAAAzB,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ,EACA3C,OAAO,CAAC+E,WAAW,iBAClBpF,OAAA;cACE4E,IAAI,EAAEvE,OAAO,CAAC+E,WAAY;cAC1BI,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBhD,KAAK,EAAE;gBACLE,OAAO,EAAE,aAAa;gBACtBsB,eAAe,EAAE,SAAS;gBAC1BhB,KAAK,EAAE,OAAO;gBACdyC,cAAc,EAAE,MAAM;gBACtBxB,YAAY,EAAE,KAAK;gBACnBG,QAAQ,EAAE;cACZ,CAAE;cAAAzB,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ,EACA3C,OAAO,CAACgF,YAAY,iBACnBrF,OAAA;cACE4E,IAAI,EAAEvE,OAAO,CAACgF,YAAa;cAC3BG,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBhD,KAAK,EAAE;gBACLE,OAAO,EAAE,aAAa;gBACtBsB,eAAe,EAAE,SAAS;gBAC1BhB,KAAK,EAAE,OAAO;gBACdyC,cAAc,EAAE,MAAM;gBACtBxB,YAAY,EAAE,KAAK;gBACnBG,QAAQ,EAAE;cACZ,CAAE;cAAAzB,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ,EACA3C,OAAO,CAACiF,WAAW,iBAClBtF,OAAA;cACE4E,IAAI,EAAEvE,OAAO,CAACiF,WAAY;cAC1BE,MAAM,EAAC,QAAQ;cACfC,GAAG,EAAC,qBAAqB;cACzBhD,KAAK,EAAE;gBACLE,OAAO,EAAE,aAAa;gBACtBsB,eAAe,EAAE,SAAS;gBAC1BhB,KAAK,EAAE,OAAO;gBACdyC,cAAc,EAAE,MAAM;gBACtBxB,YAAY,EAAE,KAAK;gBACnBG,QAAQ,EAAE;cACZ,CAAE;cAAAzB,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA,CAAC3C,OAAO,CAACsF,OAAO,IAAItF,OAAO,CAACuF,IAAI,IAAIvF,OAAO,CAACwF,KAAK,IAAIxF,OAAO,CAACyF,GAAG,IAAIzF,OAAO,CAAC0F,OAAO,kBAClF/F,OAAA;UAAKyC,KAAK,EAAE;YAAEuB,YAAY,EAAE;UAAS,CAAE;UAAApB,QAAA,gBACrC5C,OAAA;YAAIyC,KAAK,EAAE;cAAEuB,YAAY,EAAE,MAAM;cAAEf,KAAK,EAAE;YAAU,CAAE;YAAAL,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEhD,OAAA;YAAA4C,QAAA,GACGvC,OAAO,CAACsF,OAAO,iBAAI3F,OAAA;cAAA4C,QAAA,EAAMvC,OAAO,CAACsF;YAAO;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDhD,OAAA;cAAA4C,QAAA,GACG,CAACvC,OAAO,CAACuF,IAAI,EAAEvF,OAAO,CAACwF,KAAK,EAAExF,OAAO,CAACyF,GAAG,CAAC,CAACE,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EACrE7F,OAAO,CAAC0F,OAAO,iBAAI/F,OAAA;gBAAA4C,QAAA,EAAMvC,OAAO,CAAC0F;cAAO;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGA3C,OAAO,CAAC8F,YAAY,IAAIC,MAAM,CAACC,IAAI,CAAChG,OAAO,CAAC8F,YAAY,CAAC,CAACG,MAAM,GAAG,CAAC,iBACnEtG,OAAA,CAACuG,qBAAqB;UAACJ,YAAY,EAAE9F,OAAO,CAAC8F;QAAa;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAC7D,EAGA3C,OAAO,CAACmG,KAAK,iBACZxG,OAAA;UAAA4C,QAAA,gBACE5C,OAAA;YAAIyC,KAAK,EAAE;cAAEuB,YAAY,EAAE,MAAM;cAAEf,KAAK,EAAE;YAAU,CAAE;YAAAL,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjEhD,OAAA;YAAKyC,KAAK,EAAE;cACVE,OAAO,EAAE,MAAM;cACfsB,eAAe,EAAE,SAAS;cAC1BC,YAAY,EAAE,KAAK;cACnBG,QAAQ,EAAE,QAAQ;cAClBoC,UAAU,EAAE;YACd,CAAE;YAAA7D,QAAA,EACCvC,OAAO,CAACmG;UAAK;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGNhD,OAAA;QAAKyC,KAAK,EAAE;UACVwB,eAAe,EAAE,OAAO;UACxBC,YAAY,EAAE,MAAM;UACpBC,SAAS,EAAE,2BAA2B;UACtCuC,SAAS,EAAE,OAAO;UAClBjD,OAAO,EAAE,MAAM;UACfK,aAAa,EAAE;QACjB,CAAE;QAAAlB,QAAA,gBACA5C,OAAA;UAAKyC,KAAK,EAAE;YACVE,OAAO,EAAE,QAAQ;YACjBgE,YAAY,EAAE,gBAAgB;YAC9BrC,UAAU,EAAE,MAAM;YAClBD,QAAQ,EAAE,QAAQ;YAClBpB,KAAK,EAAE,SAAS;YAChBQ,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BC,UAAU,EAAE;UACd,CAAE;UAAAf,QAAA,gBACA5C,OAAA;YAAA4C,QAAA,GAAM,8BAAkB,EAACrC,aAAa,CAAC+F,MAAM,EAAC,GAAC;UAAA;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtDhD,OAAA;YACEkD,SAAS,EAAC,wBAAwB;YAClCC,OAAO,EAAEA,CAAA,KAAMrC,uBAAuB,CAAC,IAAI,CAAE;YAC7C2B,KAAK,EAAE;cACLyB,YAAY,EAAE,KAAK;cACnBvB,OAAO,EAAE,aAAa;cACtB0B,QAAQ,EAAE;YACZ,CAAE;YAAAzB,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhD,OAAA;UAAKyC,KAAK,EAAE;YACVE,OAAO,EAAE,QAAQ;YACjBiE,IAAI,EAAE,CAAC;YACPC,SAAS,EAAE,MAAM;YACjBpD,OAAO,EAAE,MAAM;YACfK,aAAa,EAAE;UACjB,CAAE;UAAAlB,QAAA,EACCrC,aAAa,CAAC+F,MAAM,KAAK,CAAC,gBACzBtG,OAAA;YAAKyC,KAAK,EAAE;cACVC,SAAS,EAAE,QAAQ;cACnBC,OAAO,EAAE,WAAW;cACpBM,KAAK,EAAE,SAAS;cAChB2D,IAAI,EAAE,CAAC;cACPnD,OAAO,EAAE,MAAM;cACfK,aAAa,EAAE,QAAQ;cACvBJ,cAAc,EAAE,QAAQ;cACxBC,UAAU,EAAE;YACd,CAAE;YAAAf,QAAA,gBACA5C,OAAA;cAAKyC,KAAK,EAAE;gBAAE4B,QAAQ,EAAE,MAAM;gBAAEL,YAAY,EAAE;cAAO,CAAE;cAAApB,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChEhD,OAAA;cAAKyC,KAAK,EAAE;gBAAE4B,QAAQ,EAAE,QAAQ;gBAAEL,YAAY,EAAE;cAAS,CAAE;cAAApB,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtFhD,OAAA;cAAKyC,KAAK,EAAE;gBAAE4B,QAAQ,EAAE;cAAS,CAAE;cAAAzB,QAAA,EAAC;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,GAERoD,MAAM,CAACU,OAAO,CAACxD,kBAAkB,CAAC,CAACyD,GAAG,CAAC,CAAC,CAAChF,IAAI,EAAEiF,gBAAgB,CAAC,kBAC9DhH,OAAA;YAAgByC,KAAK,EAAE;cAAEuB,YAAY,EAAE;YAAO,CAAE;YAAApB,QAAA,gBAC9C5C,OAAA;cAAKyC,KAAK,EAAE;gBACVC,SAAS,EAAE,QAAQ;gBACnBc,MAAM,EAAE,QAAQ;gBAChBb,OAAO,EAAE,QAAQ;gBACjBsB,eAAe,EAAE,SAAS;gBAC1BC,YAAY,EAAE,KAAK;gBACnBG,QAAQ,EAAE,QAAQ;gBAClBpB,KAAK,EAAE;cACT,CAAE;cAAAL,QAAA,EACCb;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,EAELgE,gBAAgB,CAACD,GAAG,CAAEE,YAAY,iBACjCjH,OAAA;cAEEyC,KAAK,EAAE;gBACLgB,OAAO,EAAE,MAAM;gBACfC,cAAc,EAAEuD,YAAY,CAACC,SAAS,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;gBAC7ElD,YAAY,EAAE;cAChB,CAAE;cAAApB,QAAA,eAEF5C,OAAA;gBAAKyC,KAAK,EAAE;kBACVc,QAAQ,EAAE,KAAK;kBACfZ,OAAO,EAAE,MAAM;kBACfuB,YAAY,EAAE,MAAM;kBACpBD,eAAe,EAAEgD,YAAY,CAACC,SAAS,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;kBAC1EjE,KAAK,EAAEgE,YAAY,CAACC,SAAS,KAAK,MAAM,GAAG,OAAO,GAAG;gBACvD,CAAE;gBAAAtE,QAAA,GACCqE,YAAY,CAACE,OAAO,iBACnBnH,OAAA;kBAAKyC,KAAK,EAAE;oBACV6B,UAAU,EAAE,MAAM;oBAClBN,YAAY,EAAE,QAAQ;oBACtBK,QAAQ,EAAE;kBACZ,CAAE;kBAAAzB,QAAA,EACCqE,YAAY,CAACE;gBAAO;kBAAAtE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CACN,eACDhD,OAAA;kBAAKyC,KAAK,EAAE;oBAAEuB,YAAY,EAAE;kBAAS,CAAE;kBAAApB,QAAA,EACpCqE,YAAY,CAACG;gBAAO;kBAAAvE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNhD,OAAA;kBAAKyC,KAAK,EAAE;oBACV4B,QAAQ,EAAE,QAAQ;oBAClBgD,OAAO,EAAE,GAAG;oBACZ3E,SAAS,EAAE;kBACb,CAAE;kBAAAE,QAAA,GACC,IAAIZ,IAAI,CAACiF,YAAY,CAACnF,SAAS,CAAC,CAACwF,kBAAkB,CAAC,CAAC,EACrDL,YAAY,CAAClC,MAAM,KAAK,MAAM,iBAC7B/E,OAAA;oBAAMyC,KAAK,EAAE;sBAAEY,UAAU,EAAE;oBAAS,CAAE;oBAAAT,QAAA,GAAC,SACnC,EAACqE,YAAY,CAAClC,MAAM;kBAAA;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAtCDiE,YAAY,CAAC9G,EAAE;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuCjB,CACN,CAAC;UAAA,GAvDMjB,IAAI;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwDT,CACN;QACA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAELnC,oBAAoB,iBACnBb,OAAA,CAACH,gBAAgB;MACf0H,SAAS,EAAEpH,EAAG;MACdqH,OAAO,EAAEA,CAAA,KAAM1G,uBAAuB,CAAC,KAAK,CAAE;MAC9C2G,qBAAqB,EAAE9F;IAA0B;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CACF,EAEAjC,eAAe,iBACdf,OAAA,CAACF,WAAW;MACVO,OAAO,EAAEA,OAAQ;MACjBmH,OAAO,EAAEA,CAAA,KAAMxG,kBAAkB,CAAC,KAAK,CAAE;MACzC0G,gBAAgB,EAAE9F;IAAqB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA9C,EAAA,CAleMD,aAAa;EAAA,QACFR,SAAS,EACPC,WAAW;AAAA;AAAAiI,EAAA,GAFxB1H,aAAa;AAmenB,MAAMsG,qBAAqB,GAAGA,CAAC;EAAEJ;AAAa,CAAC,KAAK;EAAAyB,GAAA;EAClD,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvI,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE5D,MAAMwI,aAAa,GAAIC,WAAW,IAAK;IACrCF,mBAAmB,CAACG,IAAI,KAAK;MAC3B,GAAGA,IAAI;MACP,CAACD,WAAW,GAAG,CAACC,IAAI,CAACD,WAAW;IAClC,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAIC,MAAM,IAAK;IACnC,MAAMC,UAAU,GAAG;MACjB,mBAAmB,EAAE,EAAE;MACvB,iBAAiB,EAAE,EAAE;MACrB,mBAAmB,EAAE,EAAE;MACvB,aAAa,EAAE,EAAE;MACjB,OAAO,EAAE;IACX,CAAC;IAEDhC,MAAM,CAACU,OAAO,CAACqB,MAAM,CAAC,CAAC7F,OAAO,CAAC,CAAC,CAAC+F,SAAS,EAAEC,KAAK,CAAC,KAAK;MACrD,MAAMC,cAAc,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC;MAE9C,IAAID,cAAc,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,cAAc,CAACE,QAAQ,CAAC,WAAW,CAAC,EAAE;QAC/EL,UAAU,CAAC,mBAAmB,CAAC,CAAC5F,IAAI,CAAC,CAAC6F,SAAS,EAAEC,KAAK,CAAC,CAAC;MAC1D,CAAC,MAAM,IAAIC,cAAc,CAACE,QAAQ,CAAC,cAAc,CAAC,IAAI,CAACF,cAAc,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAE;QACpFL,UAAU,CAAC,mBAAmB,CAAC,CAAC5F,IAAI,CAAC,CAAC6F,SAAS,EAAEC,KAAK,CAAC,CAAC;MAC1D,CAAC,MAAM,IAAIC,cAAc,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,cAAc,CAACG,UAAU,CAAC,IAAI,CAAC,EAAE;QAC5EN,UAAU,CAAC,aAAa,CAAC,CAAC5F,IAAI,CAAC,CAAC6F,SAAS,EAAEC,KAAK,CAAC,CAAC;MACpD,CAAC,MAAM,IAAIC,cAAc,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAIF,cAAc,CAACE,QAAQ,CAAC,SAAS,CAAC,IAC1EF,cAAc,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,cAAc,CAACE,QAAQ,CAAC,UAAU,CAAC,EAAE;QACpFL,UAAU,CAAC,iBAAiB,CAAC,CAAC5F,IAAI,CAAC,CAAC6F,SAAS,EAAEC,KAAK,CAAC,CAAC;MACxD,CAAC,MAAM;QACLF,UAAU,CAAC,OAAO,CAAC,CAAC5F,IAAI,CAAC,CAAC6F,SAAS,EAAEC,KAAK,CAAC,CAAC;MAC9C;IACF,CAAC,CAAC;;IAEF;IACA,OAAOlC,MAAM,CAACuC,WAAW,CACvBvC,MAAM,CAACU,OAAO,CAACsB,UAAU,CAAC,CAACpC,MAAM,CAAC,CAAC,CAAC4C,CAAC,EAAET,MAAM,CAAC,KAAKA,MAAM,CAAC7B,MAAM,GAAG,CAAC,CACtE,CAAC;EACH,CAAC;EAED,MAAMuC,iBAAiB,GAAGX,gBAAgB,CAAC/B,YAAY,CAAC;EAExD,MAAM2C,gBAAgB,GAAIR,KAAK,IAAK;IAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAChC,MAAM,GAAG,GAAG,EAAE;MACnD,oBACEtG,OAAA;QAAKyC,KAAK,EAAE;UAAE4B,QAAQ,EAAE,QAAQ;UAAEoC,UAAU,EAAE;QAAM,CAAE;QAAA7D,QAAA,eACpD5C,OAAA;UAAKyC,KAAK,EAAE;YACVsG,SAAS,EAAE,OAAO;YAClBlC,SAAS,EAAE,MAAM;YACjBlE,OAAO,EAAE,QAAQ;YACjBsB,eAAe,EAAE,SAAS;YAC1B+E,MAAM,EAAE,mBAAmB;YAC3B9E,YAAY,EAAE;UAChB,CAAE;UAAAtB,QAAA,EACC0F;QAAK;UAAAzF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IACA,oBACEhD,OAAA;MAAKyC,KAAK,EAAE;QAAE4B,QAAQ,EAAE,QAAQ;QAAEoC,UAAU,EAAE;MAAM,CAAE;MAAA7D,QAAA,EACnD0F;IAAK;MAAAzF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;EAED,oBACEhD,OAAA;IAAKyC,KAAK,EAAE;MAAEuB,YAAY,EAAE;IAAS,CAAE;IAAApB,QAAA,gBACrC5C,OAAA;MAAIyC,KAAK,EAAE;QAAEuB,YAAY,EAAE,MAAM;QAAEf,KAAK,EAAE;MAAU,CAAE;MAAAL,QAAA,EAAC;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEjFoD,MAAM,CAACU,OAAO,CAAC+B,iBAAiB,CAAC,CAAC9B,GAAG,CAAC,CAAC,CAACkC,YAAY,EAAEd,MAAM,CAAC,kBAC5DnI,OAAA;MAAwByC,KAAK,EAAE;QAAEuB,YAAY,EAAE;MAAO,CAAE;MAAApB,QAAA,gBACtD5C,OAAA;QACEmD,OAAO,EAAEA,CAAA,KAAM4E,aAAa,CAACkB,YAAY,CAAE;QAC3CxG,KAAK,EAAE;UACLyG,KAAK,EAAE,MAAM;UACbvG,OAAO,EAAE,SAAS;UAClBsB,eAAe,EAAE,SAAS;UAC1B+E,MAAM,EAAE,mBAAmB;UAC3B9E,YAAY,EAAE,KAAK;UACnBiF,MAAM,EAAE,SAAS;UACjB1F,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE,QAAQ;UACpBU,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,MAAM;UAClBrB,KAAK,EAAE,SAAS;UAChBmG,UAAU,EAAE;QACd,CAAE;QACFC,YAAY,EAAGC,CAAC,IAAK;UACnBA,CAAC,CAAC9D,MAAM,CAAC/C,KAAK,CAACwB,eAAe,GAAG,SAAS;QAC5C,CAAE;QACFsF,YAAY,EAAGD,CAAC,IAAK;UACnBA,CAAC,CAAC9D,MAAM,CAAC/C,KAAK,CAACwB,eAAe,GAAG,SAAS;QAC5C,CAAE;QAAArB,QAAA,gBAEF5C,OAAA;UAAA4C,QAAA,GAAOqG,YAAY,EAAC,IAAE,EAACd,MAAM,CAAC7B,MAAM,EAAC,GAAC;QAAA;UAAAzD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7ChD,OAAA;UAAMyC,KAAK,EAAE;YACX+G,SAAS,EAAE3B,gBAAgB,CAACoB,YAAY,CAAC,GAAG,gBAAgB,GAAG,cAAc;YAC7EG,UAAU,EAAE;UACd,CAAE;UAAAxG,QAAA,EAAC;QAEH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAER6E,gBAAgB,CAACoB,YAAY,CAAC,iBAC7BjJ,OAAA;QAAKyC,KAAK,EAAE;UACVW,SAAS,EAAE,QAAQ;UACnBT,OAAO,EAAE,MAAM;UACfsB,eAAe,EAAE,SAAS;UAC1B+E,MAAM,EAAE,mBAAmB;UAC3BS,SAAS,EAAE,MAAM;UACjBvF,YAAY,EAAE,aAAa;UAC3BT,OAAO,EAAE,MAAM;UACfiB,mBAAmB,EAAE,sCAAsC;UAC3DX,GAAG,EAAE;QACP,CAAE;QAAAnB,QAAA,EACCuF,MAAM,CAACpB,GAAG,CAAC,CAAC,CAACsB,SAAS,EAAEC,KAAK,CAAC,kBAC7BtI,OAAA;UAAqByC,KAAK,EAAE;YAC1BE,OAAO,EAAE,SAAS;YAClBsB,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,KAAK;YACnB8E,MAAM,EAAE;UACV,CAAE;UAAApG,QAAA,gBACA5C,OAAA;YAAKyC,KAAK,EAAE;cACV6B,UAAU,EAAE,MAAM;cAClBD,QAAQ,EAAE,QAAQ;cAClBL,YAAY,EAAE,QAAQ;cACtBf,KAAK,EAAE;YACT,CAAE;YAAAL,QAAA,EACCyF;UAAS;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,EACL8F,gBAAgB,CAACR,KAAK,CAAC;QAAA,GAdhBD,SAAS;UAAAxF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAed,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,GAjEOiG,YAAY;MAAApG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAkEjB,CACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC4E,GAAA,CAhJIrB,qBAAqB;AAAAmD,GAAA,GAArBnD,qBAAqB;AAkJ3B,eAAetG,aAAa;AAAC,IAAA0H,EAAA,EAAA+B,GAAA;AAAAC,YAAA,CAAAhC,EAAA;AAAAgC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}