.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.App-header {
  background-color: #2c3e50;
  padding: 1rem 2rem;
  color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.App-header h1 {
  margin: 0;
  font-size: 1.5rem;
}

.App-main {
  flex: 1;
  padding: 2rem;
  background-color: #f8f9fa;
}

/* Dashboard Styles */
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-header h2 {
  margin: 0;
  color: #2c3e50;
}

/* Contact List Styles */
.contact-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.contact-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.contact-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.contact-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.contact-email {
  color: #7f8c8d;
  margin-bottom: 0.5rem;
}

.contact-status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
}

.contact-status.active {
  background-color: #d4edda;
  color: #155724;
}

.contact-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.contact-status.archived {
  background-color: #f8d7da;
  color: #721c24;
}

.contact-meta {
  margin-top: 1rem;
  font-size: 0.9rem;
  color: #6c757d;
}

/* Button Styles */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover {
  background-color: #2980b9;
}

.btn-success {
  background-color: #27ae60;
  color: white;
}

.btn-success:hover {
  background-color: #229954;
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-control:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* List navigation scrolling */
.list-navigation {
  scrollbar-width: thin;
  scrollbar-color: #dee2e6 transparent;
}

.list-navigation::-webkit-scrollbar {
  height: 6px;
}

.list-navigation::-webkit-scrollbar-track {
  background: transparent;
}

.list-navigation::-webkit-scrollbar-thumb {
  background-color: #dee2e6;
  border-radius: 3px;
}

.list-navigation::-webkit-scrollbar-thumb:hover {
  background-color: #adb5bd;
}

/* List tab hover effects */
.list-tab .list-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.list-tab:hover .list-actions {
  opacity: 1;
}

/* Responsive list navigation */
@media (max-width: 768px) {
  .dashboard {
    padding: 1rem;
  }

  .list-tab button {
    padding: 0.4rem 0.8rem !important;
    font-size: 0.8rem !important;
  }

  .list-tab .list-actions button {
    width: 16px !important;
    height: 16px !important;
    font-size: 8px !important;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .dashboard-header > div {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .list-tab button span:last-child {
    display: none;
  }

  .list-tab button::after {
    content: attr(data-count);
    font-size: 0.7rem;
    margin-left: 0.25rem;
  }
}
