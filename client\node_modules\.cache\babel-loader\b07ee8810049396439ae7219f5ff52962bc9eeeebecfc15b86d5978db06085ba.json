{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Contact API\nexport const contactAPI = {\n  // Get all contacts\n  getAll: () => api.get('/contacts'),\n  // Get specific contact\n  getById: id => api.get(`/contacts/${id}`),\n  // Create new contact\n  create: contactData => api.post('/contacts', contactData),\n  // Update contact\n  update: (id, contactData) => api.put(`/contacts/${id}`, contactData),\n  // Delete contact\n  delete: id => api.delete(`/contacts/${id}`)\n};\n\n// Conversation API\nexport const conversationAPI = {\n  // Get all conversations\n  getAll: () => api.get('/conversations'),\n  // Get conversations for specific contact\n  getByContactId: contactId => api.get(`/conversations/contact/${contactId}`),\n  // Get specific conversation\n  getById: id => api.get(`/conversations/${id}`),\n  // Create new conversation\n  create: conversationData => api.post('/conversations', conversationData),\n  // Update conversation\n  update: (id, conversationData) => api.put(`/conversations/${id}`, conversationData),\n  // Delete conversation\n  delete: id => api.delete(`/conversations/${id}`)\n};\n\n// Lists API\nexport const listAPI = {\n  // Get all contact lists\n  getAll: () => api.get('/lists'),\n  // Get specific list\n  getById: id => api.get(`/lists/${id}`),\n  // Get contacts in a specific list\n  getContacts: id => api.get(`/lists/${id}/contacts`),\n  // Create new list\n  create: listData => api.post('/lists', listData),\n  // Update list\n  update: (id, listData) => api.put(`/lists/${id}`, listData),\n  // Delete list\n  delete: id => api.delete(`/lists/${id}`),\n  // Update contact count for a list\n  updateCount: id => api.post(`/lists/${id}/update-count`)\n};\n\n// Import API\nexport const importAPI = {\n  // Analyze CSV structure and get column mapping suggestions\n  analyzeCSV: file => {\n    const formData = new FormData();\n    formData.append('csvFile', file);\n    return api.post('/import/analyze', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  },\n  // Import contacts from CSV\n  uploadCSV: (file, listId = 'default', customMapping = null) => {\n    const formData = new FormData();\n    formData.append('csvFile', file);\n    formData.append('listId', listId);\n    if (customMapping) {\n      formData.append('mapping', JSON.stringify(customMapping));\n    }\n    return api.post('/import/csv', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  },\n  // Preview CSV data before importing\n  previewCSV: (file, customMapping = null) => {\n    const formData = new FormData();\n    formData.append('csvFile', file);\n    if (customMapping) {\n      formData.append('mapping', JSON.stringify(customMapping));\n    }\n    return api.post('/import/preview', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  },\n  // Download CSV template\n  downloadTemplate: () => {\n    return api.get('/import/template', {\n      responseType: 'blob'\n    });\n  }\n};\n\n// Error handling interceptor\napi.interceptors.response.use(response => response, error => {\n  console.error('API Error:', error);\n  if (error.response) {\n    var _error$response$data;\n    // Server responded with error status\n    const message = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'An error occurred';\n    throw new Error(message);\n  } else if (error.request) {\n    // Request was made but no response received\n    throw new Error('Unable to connect to server');\n  } else {\n    // Something else happened\n    throw new Error('An unexpected error occurred');\n  }\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "api", "create", "baseURL", "headers", "contactAPI", "getAll", "get", "getById", "id", "contactData", "post", "update", "put", "delete", "conversationAPI", "getByContactId", "contactId", "conversationData", "listAPI", "getContacts", "listData", "updateCount", "importAPI", "analyzeCSV", "file", "formData", "FormData", "append", "uploadCSV", "listId", "customMapping", "JSON", "stringify", "previewCSV", "downloadTemplate", "responseType", "interceptors", "response", "use", "error", "console", "_error$response$data", "message", "data", "Error", "request"], "sources": ["C:/Users/<USER>/Desktop/email_dash/client/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Contact API\nexport const contactAPI = {\n  // Get all contacts\n  getAll: () => api.get('/contacts'),\n  \n  // Get specific contact\n  getById: (id) => api.get(`/contacts/${id}`),\n  \n  // Create new contact\n  create: (contactData) => api.post('/contacts', contactData),\n  \n  // Update contact\n  update: (id, contactData) => api.put(`/contacts/${id}`, contactData),\n  \n  // Delete contact\n  delete: (id) => api.delete(`/contacts/${id}`),\n};\n\n// Conversation API\nexport const conversationAPI = {\n  // Get all conversations\n  getAll: () => api.get('/conversations'),\n  \n  // Get conversations for specific contact\n  getByContactId: (contactId) => api.get(`/conversations/contact/${contactId}`),\n  \n  // Get specific conversation\n  getById: (id) => api.get(`/conversations/${id}`),\n  \n  // Create new conversation\n  create: (conversationData) => api.post('/conversations', conversationData),\n  \n  // Update conversation\n  update: (id, conversationData) => api.put(`/conversations/${id}`, conversationData),\n  \n  // Delete conversation\n  delete: (id) => api.delete(`/conversations/${id}`),\n};\n\n// Lists API\nexport const listAPI = {\n  // Get all contact lists\n  getAll: () => api.get('/lists'),\n\n  // Get specific list\n  getById: (id) => api.get(`/lists/${id}`),\n\n  // Get contacts in a specific list\n  getContacts: (id) => api.get(`/lists/${id}/contacts`),\n\n  // Create new list\n  create: (listData) => api.post('/lists', listData),\n\n  // Update list\n  update: (id, listData) => api.put(`/lists/${id}`, listData),\n\n  // Delete list\n  delete: (id) => api.delete(`/lists/${id}`),\n\n  // Update contact count for a list\n  updateCount: (id) => api.post(`/lists/${id}/update-count`),\n};\n\n// Import API\nexport const importAPI = {\n  // Analyze CSV structure and get column mapping suggestions\n  analyzeCSV: (file) => {\n    const formData = new FormData();\n    formData.append('csvFile', file);\n\n    return api.post('/import/analyze', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n  },\n\n  // Import contacts from CSV\n  uploadCSV: (file, listId = 'default', customMapping = null) => {\n    const formData = new FormData();\n    formData.append('csvFile', file);\n    formData.append('listId', listId);\n    if (customMapping) {\n      formData.append('mapping', JSON.stringify(customMapping));\n    }\n\n    return api.post('/import/csv', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n  },\n\n  // Preview CSV data before importing\n  previewCSV: (file, customMapping = null) => {\n    const formData = new FormData();\n    formData.append('csvFile', file);\n    if (customMapping) {\n      formData.append('mapping', JSON.stringify(customMapping));\n    }\n\n    return api.post('/import/preview', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n  },\n\n  // Download CSV template\n  downloadTemplate: () => {\n    return api.get('/import/template', {\n      responseType: 'blob',\n    });\n  },\n};\n\n// Error handling interceptor\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    console.error('API Error:', error);\n    \n    if (error.response) {\n      // Server responded with error status\n      const message = error.response.data?.error || 'An error occurred';\n      throw new Error(message);\n    } else if (error.request) {\n      // Request was made but no response received\n      throw new Error('Unable to connect to server');\n    } else {\n      // Something else happened\n      throw new Error('An unexpected error occurred');\n    }\n  }\n);\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;AAEjF,MAAMC,GAAG,GAAGL,KAAK,CAACM,MAAM,CAAC;EACvBC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMC,UAAU,GAAG;EACxB;EACAC,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACM,GAAG,CAAC,WAAW,CAAC;EAElC;EACAC,OAAO,EAAGC,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EAE3C;EACAP,MAAM,EAAGQ,WAAW,IAAKT,GAAG,CAACU,IAAI,CAAC,WAAW,EAAED,WAAW,CAAC;EAE3D;EACAE,MAAM,EAAEA,CAACH,EAAE,EAAEC,WAAW,KAAKT,GAAG,CAACY,GAAG,CAAC,aAAaJ,EAAE,EAAE,EAAEC,WAAW,CAAC;EAEpE;EACAI,MAAM,EAAGL,EAAE,IAAKR,GAAG,CAACa,MAAM,CAAC,aAAaL,EAAE,EAAE;AAC9C,CAAC;;AAED;AACA,OAAO,MAAMM,eAAe,GAAG;EAC7B;EACAT,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACM,GAAG,CAAC,gBAAgB,CAAC;EAEvC;EACAS,cAAc,EAAGC,SAAS,IAAKhB,GAAG,CAACM,GAAG,CAAC,0BAA0BU,SAAS,EAAE,CAAC;EAE7E;EACAT,OAAO,EAAGC,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,kBAAkBE,EAAE,EAAE,CAAC;EAEhD;EACAP,MAAM,EAAGgB,gBAAgB,IAAKjB,GAAG,CAACU,IAAI,CAAC,gBAAgB,EAAEO,gBAAgB,CAAC;EAE1E;EACAN,MAAM,EAAEA,CAACH,EAAE,EAAES,gBAAgB,KAAKjB,GAAG,CAACY,GAAG,CAAC,kBAAkBJ,EAAE,EAAE,EAAES,gBAAgB,CAAC;EAEnF;EACAJ,MAAM,EAAGL,EAAE,IAAKR,GAAG,CAACa,MAAM,CAAC,kBAAkBL,EAAE,EAAE;AACnD,CAAC;;AAED;AACA,OAAO,MAAMU,OAAO,GAAG;EACrB;EACAb,MAAM,EAAEA,CAAA,KAAML,GAAG,CAACM,GAAG,CAAC,QAAQ,CAAC;EAE/B;EACAC,OAAO,EAAGC,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,UAAUE,EAAE,EAAE,CAAC;EAExC;EACAW,WAAW,EAAGX,EAAE,IAAKR,GAAG,CAACM,GAAG,CAAC,UAAUE,EAAE,WAAW,CAAC;EAErD;EACAP,MAAM,EAAGmB,QAAQ,IAAKpB,GAAG,CAACU,IAAI,CAAC,QAAQ,EAAEU,QAAQ,CAAC;EAElD;EACAT,MAAM,EAAEA,CAACH,EAAE,EAAEY,QAAQ,KAAKpB,GAAG,CAACY,GAAG,CAAC,UAAUJ,EAAE,EAAE,EAAEY,QAAQ,CAAC;EAE3D;EACAP,MAAM,EAAGL,EAAE,IAAKR,GAAG,CAACa,MAAM,CAAC,UAAUL,EAAE,EAAE,CAAC;EAE1C;EACAa,WAAW,EAAGb,EAAE,IAAKR,GAAG,CAACU,IAAI,CAAC,UAAUF,EAAE,eAAe;AAC3D,CAAC;;AAED;AACA,OAAO,MAAMc,SAAS,GAAG;EACvB;EACAC,UAAU,EAAGC,IAAI,IAAK;IACpB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,IAAI,CAAC;IAEhC,OAAOxB,GAAG,CAACU,IAAI,CAAC,iBAAiB,EAAEe,QAAQ,EAAE;MAC3CtB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAyB,SAAS,EAAEA,CAACJ,IAAI,EAAEK,MAAM,GAAG,SAAS,EAAEC,aAAa,GAAG,IAAI,KAAK;IAC7D,MAAML,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,IAAI,CAAC;IAChCC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEE,MAAM,CAAC;IACjC,IAAIC,aAAa,EAAE;MACjBL,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEI,IAAI,CAACC,SAAS,CAACF,aAAa,CAAC,CAAC;IAC3D;IAEA,OAAO9B,GAAG,CAACU,IAAI,CAAC,aAAa,EAAEe,QAAQ,EAAE;MACvCtB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACA8B,UAAU,EAAEA,CAACT,IAAI,EAAEM,aAAa,GAAG,IAAI,KAAK;IAC1C,MAAML,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEH,IAAI,CAAC;IAChC,IAAIM,aAAa,EAAE;MACjBL,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEI,IAAI,CAACC,SAAS,CAACF,aAAa,CAAC,CAAC;IAC3D;IAEA,OAAO9B,GAAG,CAACU,IAAI,CAAC,iBAAiB,EAAEe,QAAQ,EAAE;MAC3CtB,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACA+B,gBAAgB,EAAEA,CAAA,KAAM;IACtB,OAAOlC,GAAG,CAACM,GAAG,CAAC,kBAAkB,EAAE;MACjC6B,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACAnC,GAAG,CAACoC,YAAY,CAACC,QAAQ,CAACC,GAAG,CAC1BD,QAAQ,IAAKA,QAAQ,EACrBE,KAAK,IAAK;EACTC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EAElC,IAAIA,KAAK,CAACF,QAAQ,EAAE;IAAA,IAAAI,oBAAA;IAClB;IACA,MAAMC,OAAO,GAAG,EAAAD,oBAAA,GAAAF,KAAK,CAACF,QAAQ,CAACM,IAAI,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBF,KAAK,KAAI,mBAAmB;IACjE,MAAM,IAAIK,KAAK,CAACF,OAAO,CAAC;EAC1B,CAAC,MAAM,IAAIH,KAAK,CAACM,OAAO,EAAE;IACxB;IACA,MAAM,IAAID,KAAK,CAAC,6BAA6B,CAAC;EAChD,CAAC,MAAM;IACL;IACA,MAAM,IAAIA,KAAK,CAAC,8BAA8B,CAAC;EACjD;AACF,CACF,CAAC;AAED,eAAe5C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}